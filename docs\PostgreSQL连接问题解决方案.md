# PostgreSQL连接问题解决方案

## 🐛 问题描述

用户切换到PostgreSQL配置后遇到数据库表创建失败的问题：

```
22:39:43 [信息] db.session - ✅ 数据库连接成功
22:39:43 [错误] main - ❌ 数据库表创建失败: 'NoneType' object has no attribute '_run_ddl_visitor'
22:39:43 [警告] main - ⚠️ 数据库功能可能不完整
```

## 🔍 问题分析

### 1. 根本原因

**引擎对象为None**：
- 错误信息 `'NoneType' object has no attribute '_run_ddl_visitor'` 表示 `engine` 对象为 `None`
- 这是因为在 `main.py` 导入时，`engine` 还没有被 `init_database()` 初始化

### 2. 导入时机问题

**问题代码**：
```python
# main.py
from app.db.session import engine  # ❌ 导入时 engine = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    init_database()  # 这时才初始化 engine
    Base.metadata.create_all(bind=engine)  # ❌ 使用的还是导入时的 None
```

### 3. PostgreSQL驱动缺失

**附加问题**：
- 用户配置了PostgreSQL但缺少 `psycopg2` 驱动
- 导致连接初始化失败，engine 保持为 None

## ✅ 解决方案

### 1. 修复引擎访问逻辑

#### 修改 `main.py`

**修复前**：
```python
from app.db.session import init_database, get_database_status, engine  # ❌

if db_success and db_status['available']:
    Base.metadata.create_all(bind=engine)  # ❌ engine 可能为 None
```

**修复后**：
```python
from app.db.session import init_database, get_database_status, get_engine  # ✅

if db_success and db_status['available']:
    engine = get_engine()  # ✅ 运行时获取已初始化的 engine
    if engine is not None:
        Base.metadata.create_all(bind=engine)
    else:
        logger.error("❌ 数据库引擎未正确初始化")
```

#### 添加 `get_engine()` 函数

**在 `app/db/session.py` 中添加**：
```python
def get_engine():
    """获取数据库引擎"""
    return engine
```

### 2. PostgreSQL驱动安装

#### 安装方法

```bash
# 方法1: 使用 pip
pip install psycopg2-binary

# 方法2: 如果遇到权限问题
pip install --user psycopg2-binary

# 方法3: 使用 poetry
poetry add psycopg2-binary

# 方法4: 如果网络有问题，使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple psycopg2-binary
```

#### 验证安装

```python
python -c "import psycopg2; print('✅ PostgreSQL驱动安装成功')"
```

### 3. 配置选择建议

#### 选项1: 使用SQLite (推荐开发)

```bash
# 在 .env 文件中
# DATABASE_URL=postgresql://smartscoring:password123@localhost:5432/smartscoring_db
DATABASE_URL=sqlite:///./smartscoring.db
```

**优点**：
- 无需额外安装驱动
- 无需配置数据库服务器
- 适合开发和测试

#### 选项2: 继续使用PostgreSQL (推荐生产)

```bash
# 1. 安装驱动
pip install psycopg2-binary

# 2. 确保PostgreSQL服务运行
# Windows: 启动PostgreSQL服务
# Linux: sudo systemctl start postgresql

# 3. 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE smartscoring_db;
CREATE USER smartscoring WITH PASSWORD 'password123';
GRANT ALL PRIVILEGES ON DATABASE smartscoring_db TO smartscoring;
\q

# 4. 在 .env 文件中配置
DATABASE_URL=postgresql://smartscoring:password123@localhost:5432/smartscoring_db
```

#### 选项3: 无数据库模式 (快速体验)

```bash
# 在 .env 文件中
DATABASE_URL=
```

**适用场景**：
- 只需要OCR识别功能
- 快速体验和测试
- 不需要用户管理和历史记录

## 🧪 测试验证

### 修复后的预期日志

#### SQLite模式：
```
🚀 正在启动 Smart Scoring API...
✅ 数据库连接成功
✅ 数据库表创建/检查完成
✅ 数据库功能已启用
📊 数据库状态: {'available': True, 'configured': True, 'engine_created': True, 'session_factory_created': True}
🎉 Smart Scoring API 启动完成
```

#### PostgreSQL模式（有驱动）：
```
🚀 正在启动 Smart Scoring API...
✅ 数据库连接成功
✅ 数据库表创建/检查完成
✅ 数据库功能已启用
📊 数据库状态: {'available': True, 'configured': True, 'engine_created': True, 'session_factory_created': True}
🎉 Smart Scoring API 启动完成
```

#### PostgreSQL模式（无驱动）：
```
🚀 正在启动 Smart Scoring API...
❌ 数据库初始化异常: No module named 'psycopg2'
⚠️ 数据库功能将不可用
⚠️ 应用将在无数据库模式下运行
💡 某些功能（如用户管理、评分历史）将不可用
✅ OCR识别和评分计算功能仍然可用
📊 数据库状态: {'available': False, 'configured': True, 'engine_created': False, 'session_factory_created': False}
🎉 Smart Scoring API 启动完成
```

## 🎯 推荐解决步骤

### 快速解决（推荐）

1. **切换到SQLite**：
   ```bash
   # 编辑 .env 文件
   # DATABASE_URL=postgresql://smartscoring:password123@localhost:5432/smartscoring_db
   DATABASE_URL=sqlite:///./smartscoring.db
   ```

2. **重启应用**：
   ```bash
   python main.py
   ```

3. **验证功能**：
   - 访问 http://localhost:8000/api/v1/docs
   - 测试OCR功能
   - 检查数据库功能

### 完整PostgreSQL配置

1. **安装驱动**：
   ```bash
   pip install psycopg2-binary
   ```

2. **启动PostgreSQL服务**：
   ```bash
   # Windows
   net start postgresql-x64-13
   
   # Linux
   sudo systemctl start postgresql
   ```

3. **创建数据库**：
   ```sql
   CREATE DATABASE smartscoring_db;
   CREATE USER smartscoring WITH PASSWORD 'password123';
   GRANT ALL PRIVILEGES ON DATABASE smartscoring_db TO smartscoring;
   ```

4. **配置连接**：
   ```bash
   DATABASE_URL=postgresql://smartscoring:password123@localhost:5432/smartscoring_db
   ```

5. **初始化数据库**：
   ```bash
   python init_db.py
   ```

6. **启动应用**：
   ```bash
   python main.py
   ```

## 🔧 故障排除

### 如果仍然遇到问题

1. **检查PostgreSQL服务**：
   ```bash
   # Windows
   sc query postgresql-x64-13
   
   # Linux
   sudo systemctl status postgresql
   ```

2. **测试数据库连接**：
   ```bash
   psql -h localhost -U smartscoring -d smartscoring_db
   ```

3. **检查防火墙设置**：
   - 确保5432端口开放
   - 检查PostgreSQL配置文件

4. **查看详细错误**：
   ```bash
   # 启用调试模式
   DEBUG=true python main.py
   ```

## 🎉 总结

通过这次修复：

1. **解决了引擎访问问题** - 使用运行时获取而不是导入时获取
2. **提供了多种配置选择** - SQLite、PostgreSQL、无数据库模式
3. **改善了错误处理** - 优雅降级和清晰的错误提示
4. **增强了系统稳定性** - 即使配置有问题也能正常启动

现在用户可以：
- 根据需求选择合适的数据库配置
- 享受稳定的启动体验
- 获得清晰的状态反馈
- 在任何情况下都能使用OCR功能

系统现在更加健壮和用户友好！🎉
