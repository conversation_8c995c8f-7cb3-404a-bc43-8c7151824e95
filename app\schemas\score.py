"""
评分相关的数据模式
定义评分请求、响应和计算结果等数据结构
"""

from typing import List, Optional, Any, Dict
from pydantic import BaseModel, Field


class ScoreCalculationRequest(BaseModel):
    """评分计算请求模式"""

    config_id: int = Field(..., description="权重配置ID")
    situation: Optional[str] = Field(None, description="情境标识符（可选，不提供则自动判断）")

    class Config:
        json_schema_extra = {
            "example": {
                "config_id": 1,
                "situation": "4"
            }
        }


class TermBreakdown(BaseModel):
    """词条评分明细"""
    
    term_name: str = Field(..., description="词条名称")
    term_type: str = Field(..., description="词条类型", pattern="^(main_prop|sub_prop)$")
    source_text: str = Field(..., description="OCR识别的原始文本")
    extracted_value: float = Field(..., description="提取的数值")
    weight: float = Field(..., description="权重值")
    score: float = Field(..., description="计算得分")
    
    class Config:
        json_schema_extra = {
            "example": {
                "term_name": "词条A",
                "term_type": "main_prop",
                "source_text": "词条A: 30%",
                "extracted_value": 0.3,
                "weight": 0.5,
                "score": 10.0
            }
        }


class CalculationContext(BaseModel):
    """计算上下文信息"""
    
    config_name: str = Field(..., description="使用的配置名称")
    config_id: int = Field(..., description="配置ID")
    situation: str = Field(..., description="情境标识符")
    max_unaligned_score: float = Field(..., description="未对齐最高分")
    aligned_score: float = Field(default=50.0, description="对齐分数")
    
    class Config:
        json_schema_extra = {
            "example": {
                "config_name": "某角色-配置A",
                "config_id": 1,
                "situation": "c4",
                "max_unaligned_score": 75.0,
                "aligned_score": 50.0
            }
        }


class OCRResult(BaseModel):
    """OCR识别结果"""
    
    raw_text: str = Field(..., description="原始识别文本")
    confidence: float = Field(..., description="识别置信度")
    processing_time: float = Field(..., description="处理时间（秒）")
    
    class Config:
        json_schema_extra = {
            "example": {
                "raw_text": "词条A: 30%\n词条C数值64",
                "confidence": 0.95,
                "processing_time": 1.23
            }
        }


class ScoreCalculationResponse(BaseModel):
    """评分计算响应模式"""

    total_score: float = Field(..., description="总分")
    calculation_context: CalculationContext = Field(..., description="计算上下文")
    breakdown: List[TermBreakdown] = Field(..., description="评分明细")
    ocr_result: OCRResult = Field(..., description="OCR识别结果")
    valid_terms_count: int = Field(..., description="有效词条数量")
    invalid_terms: List[str] = Field(default=[], description="无效词条列表")
    validation_errors: List[str] = Field(default=[], description="词条验证错误列表")
    main_props_found: List[str] = Field(default=[], description="识别到的主词条列表")
    sub_props_found: List[str] = Field(default=[], description="识别到的副词条列表")
    auto_detected_situation: Optional[str] = Field(None, description="自动检测的情况（如果适用）")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_score": 52.75,
                "calculation_context": {
                    "config_name": "某角色-配置A",
                    "config_id": 1,
                    "situation": "c4",
                    "max_unaligned_score": 75.0,
                    "aligned_score": 50.0
                },
                "breakdown": [
                    {
                        "term_name": "词条A",
                        "term_type": "main_prop",
                        "source_text": "词条A: 30%",
                        "extracted_value": 0.3,
                        "weight": 0.5,
                        "score": 10.0
                    },
                    {
                        "term_name": "词条C",
                        "term_type": "sub_prop",
                        "source_text": "词条C数值64",
                        "extracted_value": 64,
                        "weight": 1.0,
                        "score": 42.75
                    }
                ],
                "ocr_result": {
                    "raw_text": "词条A: 30%\n词条C数值64",
                    "confidence": 0.95,
                    "processing_time": 1.23
                },
                "valid_terms_count": 2,
                "invalid_terms": []
            }
        }


class Base64ImageRequest(BaseModel):
    """Base64图片评分请求模式"""

    image_data: str = Field(..., description="Base64编码的图片数据")
    config_id: Optional[int] = Field(None, description="权重配置ID（与character_name二选一）")
    character_name: Optional[str] = Field(None, description="角色名称（与config_id二选一）")
    situation: Optional[str] = Field(None, description="情境标识符（可选，不提供则自动判断）")

    class Config:
        json_schema_extra = {
            "example": {
                "image_data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
                "config_id": 43,
                "situation": "3"
            }
        }


# 别名管理相关模式
class AliasInfo(BaseModel):
    """别名信息"""
    character_name: str = Field(..., description="角色名称")
    aliases: List[str] = Field(..., description="别名列表")


class AliasListResponse(BaseModel):
    """别名列表响应"""
    aliases: Dict[str, List[str]] = Field(..., description="别名映射")
    total_characters: int = Field(..., description="角色总数")

    class Config:
        json_schema_extra = {
            "example": {
                "aliases": {
                    "布兰特": ["布兰特", "布兰", "兰特", "船长"],
                    "今汐": ["今汐", "汐汐", "jinxi"]
                },
                "total_characters": 2
            }
        }


class AliasUpdateRequest(BaseModel):
    """别名更新请求"""
    character_name: str = Field(..., description="角色名称")
    aliases: List[str] = Field(..., description="新的别名列表")

    class Config:
        json_schema_extra = {
            "example": {
                "character_name": "布兰特",
                "aliases": ["布兰特", "布兰", "兰特", "船长", "bulante"]
            }
        }


class AliasUpdateResponse(BaseModel):
    """别名更新响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="操作结果信息")
    character_name: str = Field(..., description="角色名称")
    aliases: List[str] = Field(..., description="更新后的别名列表")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "别名更新成功",
                "character_name": "布兰特",
                "aliases": ["布兰特", "布兰", "兰特", "船长", "bulante"]
            }
        }


class ErrorResponse(BaseModel):
    """错误响应模式"""

    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Any] = Field(None, description="错误详情")

    class Config:
        json_schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "配置ID不存在",
                "details": {"config_id": 999}
            }
        }
