"""
Pytest配置文件
定义测试的全局fixtures和配置
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.api.deps import get_db
from app.core.config import settings
from app.db.base import Base
from main import app

# 测试数据库URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

# 创建测试数据库引擎
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, 
    connect_args={"check_same_thread": False}
)

# 创建测试会话工厂
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def db():
    """创建测试数据库"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def db_session(db):
    """创建数据库会话"""
    connection = engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    yield session
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def client(db_session):
    """创建测试客户端"""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    yield TestClient(app)
    app.dependency_overrides.clear()


@pytest.fixture
def admin_token(client, db_session):
    """获取管理员令牌"""
    from app.crud import user
    from app.schemas.user import UserCreate
    
    # 创建管理员用户
    admin_data = UserCreate(
        username="testadmin",
        email="<EMAIL>",
        password="testpass123",
        role="admin"
    )
    user.create(db_session, obj_in=admin_data)
    
    # 登录获取令牌
    response = client.post(
        "/api/v1/auth/login-json",
        json={"username": "testadmin", "password": "testpass123"}
    )
    return response.json()["access_token"]


@pytest.fixture
def user_token(client, db_session):
    """获取普通用户令牌"""
    from app.crud import user
    from app.schemas.user import UserCreate
    
    # 创建普通用户
    user_data = UserCreate(
        username="testuser",
        email="<EMAIL>",
        password="testpass123",
        role="user"
    )
    user.create(db_session, obj_in=user_data)
    
    # 登录获取令牌
    response = client.post(
        "/api/v1/auth/login-json",
        json={"username": "testuser", "password": "testpass123"}
    )
    return response.json()["access_token"]


@pytest.fixture
def sample_config(db_session):
    """创建示例权重配置"""
    from app.crud import weight_config
    from app.schemas.weight_config import WeightConfigCreate
    
    config_data = WeightConfigCreate(
        name="测试配置",
        description="用于测试的权重配置",
        main_props={
            "c4": {"攻击力": 0.5, "暴击率": 0.8},
            "c1": {"攻击力": 0.4}
        },
        sub_props={
            "攻击力": 1.0,
            "暴击率": 1.2,
            "暴击伤害": 1.1
        },
        score_max=[75.0, 80.0],
        situation_map={"c4": 0, "c1": 1}
    )
    
    return weight_config.create(db_session, obj_in=config_data)


@pytest.fixture
def auth_headers():
    """认证头部工厂函数"""
    def _auth_headers(token: str):
        return {"Authorization": f"Bearer {token}"}
    return _auth_headers
