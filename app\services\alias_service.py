"""
别名管理服务
处理角色别名的增删改查功能
"""

import json
import os
from typing import Dict, List, Optional, Tuple
from pathlib import Path


class AliasService:
    """别名管理服务"""
    
    def __init__(self, alias_file_path: str = "character_aliases.json"):
        """
        初始化别名服务
        
        Args:
            alias_file_path: 别名配置文件路径
        """
        self.alias_file_path = alias_file_path
        self._aliases_cache = None
        self._load_aliases()
    
    def _load_aliases(self) -> None:
        """加载别名配置文件"""
        try:
            if os.path.exists(self.alias_file_path):
                with open(self.alias_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._aliases_cache = data.get("aliases", {})
            else:
                # 如果文件不存在，创建默认配置
                self._aliases_cache = {}
                self._save_aliases()
        except Exception as e:
            print(f"加载别名配置失败: {e}")
            self._aliases_cache = {}
    
    def _save_aliases(self) -> bool:
        """保存别名配置到文件"""
        try:
            # 构建完整的配置结构
            config_data = {
                "aliases": self._aliases_cache,
                "description": "角色别名配置文件",
                "version": "1.0",
                "last_updated": self._get_current_date(),
                "usage": {
                    "description": "此文件定义了角色的别名映射，支持多种别名对应到同一个角色",
                    "format": {
                        "角色名": ["别名1", "别名2", "别名3"]
                    },
                    "notes": [
                        "角色名必须与character目录下的文件夹名完全一致",
                        "别名不区分大小写",
                        "支持中文、英文、拼音等多种别名",
                        "可以添加常用的简称和昵称"
                    ]
                }
            }
            
            with open(self.alias_file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存别名配置失败: {e}")
            return False
    
    def _get_current_date(self) -> str:
        """获取当前日期字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d")
    
    def get_all_aliases(self) -> Dict[str, List[str]]:
        """
        获取所有别名配置
        
        Returns:
            别名映射字典
        """
        if self._aliases_cache is None:
            self._load_aliases()
        return self._aliases_cache.copy()
    
    def get_character_aliases(self, character_name: str) -> Optional[List[str]]:
        """
        获取指定角色的别名列表
        
        Args:
            character_name: 角色名称
            
        Returns:
            别名列表，如果角色不存在返回None
        """
        if self._aliases_cache is None:
            self._load_aliases()
        return self._aliases_cache.get(character_name)
    
    def update_character_aliases(self, character_name: str, aliases: List[str]) -> Tuple[bool, str]:
        """
        更新指定角色的别名列表
        
        Args:
            character_name: 角色名称
            aliases: 新的别名列表
            
        Returns:
            (是否成功, 结果消息)
        """
        try:
            if self._aliases_cache is None:
                self._load_aliases()
            
            # 验证别名列表
            if not aliases:
                return False, "别名列表不能为空"
            
            # 确保角色名在别名列表中
            if character_name not in aliases:
                aliases.insert(0, character_name)
            
            # 去重并保持顺序
            unique_aliases = []
            seen = set()
            for alias in aliases:
                if alias and alias not in seen:
                    unique_aliases.append(alias)
                    seen.add(alias)
            
            # 更新缓存
            self._aliases_cache[character_name] = unique_aliases
            
            # 保存到文件
            if self._save_aliases():
                return True, f"角色 '{character_name}' 的别名更新成功"
            else:
                return False, "保存配置文件失败"
                
        except Exception as e:
            return False, f"更新别名失败: {str(e)}"
    
    def add_character_alias(self, character_name: str, new_alias: str) -> Tuple[bool, str]:
        """
        为指定角色添加新别名
        
        Args:
            character_name: 角色名称
            new_alias: 新别名
            
        Returns:
            (是否成功, 结果消息)
        """
        try:
            if self._aliases_cache is None:
                self._load_aliases()
            
            if not new_alias or not new_alias.strip():
                return False, "别名不能为空"
            
            new_alias = new_alias.strip()
            
            # 检查别名是否已存在于其他角色
            for char_name, char_aliases in self._aliases_cache.items():
                if new_alias.lower() in [alias.lower() for alias in char_aliases]:
                    if char_name != character_name:
                        return False, f"别名 '{new_alias}' 已被角色 '{char_name}' 使用"
            
            # 获取当前别名列表
            current_aliases = self._aliases_cache.get(character_name, [character_name])
            
            # 检查是否已存在
            if new_alias.lower() in [alias.lower() for alias in current_aliases]:
                return False, f"别名 '{new_alias}' 已存在"
            
            # 添加新别名
            current_aliases.append(new_alias)
            
            # 更新
            return self.update_character_aliases(character_name, current_aliases)
            
        except Exception as e:
            return False, f"添加别名失败: {str(e)}"
    
    def remove_character_alias(self, character_name: str, alias_to_remove: str) -> Tuple[bool, str]:
        """
        移除指定角色的别名
        
        Args:
            character_name: 角色名称
            alias_to_remove: 要移除的别名
            
        Returns:
            (是否成功, 结果消息)
        """
        try:
            if self._aliases_cache is None:
                self._load_aliases()
            
            if character_name not in self._aliases_cache:
                return False, f"角色 '{character_name}' 不存在"
            
            current_aliases = self._aliases_cache[character_name].copy()
            
            # 不能移除角色本名
            if alias_to_remove == character_name:
                return False, "不能移除角色本名"
            
            # 查找并移除别名
            removed = False
            for i, alias in enumerate(current_aliases):
                if alias.lower() == alias_to_remove.lower():
                    current_aliases.pop(i)
                    removed = True
                    break
            
            if not removed:
                return False, f"别名 '{alias_to_remove}' 不存在"
            
            # 确保至少保留角色本名
            if not current_aliases:
                current_aliases = [character_name]
            
            # 更新
            return self.update_character_aliases(character_name, current_aliases)
            
        except Exception as e:
            return False, f"移除别名失败: {str(e)}"
    
    def delete_character(self, character_name: str) -> Tuple[bool, str]:
        """
        删除指定角色及其所有别名
        
        Args:
            character_name: 角色名称
            
        Returns:
            (是否成功, 结果消息)
        """
        try:
            if self._aliases_cache is None:
                self._load_aliases()
            
            if character_name not in self._aliases_cache:
                return False, f"角色 '{character_name}' 不存在"
            
            # 删除角色
            del self._aliases_cache[character_name]
            
            # 保存到文件
            if self._save_aliases():
                return True, f"角色 '{character_name}' 删除成功"
            else:
                return False, "保存配置文件失败"
                
        except Exception as e:
            return False, f"删除角色失败: {str(e)}"
    
    def find_character_by_alias(self, alias: str) -> Optional[str]:
        """
        根据别名查找角色名称
        
        Args:
            alias: 别名
            
        Returns:
            角色名称，如果未找到返回None
        """
        if self._aliases_cache is None:
            self._load_aliases()
        
        alias_lower = alias.lower()
        
        for character_name, aliases in self._aliases_cache.items():
            if alias_lower in [a.lower() for a in aliases]:
                return character_name
        
        return None
    
    def validate_alias_uniqueness(self, character_name: str, alias: str) -> Tuple[bool, str]:
        """
        验证别名的唯一性
        
        Args:
            character_name: 角色名称
            alias: 要验证的别名
            
        Returns:
            (是否唯一, 冲突信息)
        """
        if self._aliases_cache is None:
            self._load_aliases()
        
        alias_lower = alias.lower()
        
        for char_name, char_aliases in self._aliases_cache.items():
            if char_name != character_name:
                if alias_lower in [a.lower() for a in char_aliases]:
                    return False, f"别名 '{alias}' 已被角色 '{char_name}' 使用"
        
        return True, ""
    
    def get_statistics(self) -> Dict:
        """
        获取别名统计信息
        
        Returns:
            统计信息字典
        """
        if self._aliases_cache is None:
            self._load_aliases()
        
        total_characters = len(self._aliases_cache)
        total_aliases = sum(len(aliases) for aliases in self._aliases_cache.values())
        
        # 计算平均别名数
        avg_aliases = total_aliases / total_characters if total_characters > 0 else 0
        
        # 找出别名最多的角色
        max_aliases_char = None
        max_aliases_count = 0
        
        for char_name, aliases in self._aliases_cache.items():
            if len(aliases) > max_aliases_count:
                max_aliases_count = len(aliases)
                max_aliases_char = char_name
        
        return {
            "total_characters": total_characters,
            "total_aliases": total_aliases,
            "average_aliases_per_character": round(avg_aliases, 2),
            "character_with_most_aliases": {
                "name": max_aliases_char,
                "count": max_aliases_count
            } if max_aliases_char else None
        }


# 创建全局实例
alias_service = AliasService()
