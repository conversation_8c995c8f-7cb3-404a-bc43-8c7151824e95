"""
日志配置模块
配置应用程序的日志系统，支持中文显示和文件保存
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any

from app.core.config import settings


class ChineseFormatter(logging.Formatter):
    """支持中文的日志格式化器"""
    
    # 中文级别映射
    LEVEL_MAPPING = {
        'DEBUG': '调试',
        'INFO': '信息',
        'WARNING': '警告',
        'ERROR': '错误',
        'CRITICAL': '严重'
    }
    
    def format(self, record):
        """格式化日志记录"""
        # 转换日志级别为中文
        if hasattr(record, 'levelname'):
            record.chinese_levelname = self.LEVEL_MAPPING.get(record.levelname, record.levelname)
        
        # 格式化模块名
        if hasattr(record, 'name'):
            # 简化模块名显示
            module_parts = record.name.split('.')
            if len(module_parts) > 2:
                record.short_name = f"{module_parts[-2]}.{module_parts[-1]}"
            else:
                record.short_name = record.name
        
        return super().format(record)


class LoggingConfig:
    """日志配置类"""
    
    def __init__(self):
        """初始化日志配置"""
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)
        
        # 清理过期日志文件
        self._cleanup_old_logs()
    
    def setup_logging(self) -> None:
        """设置日志配置"""
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 设置控制台处理器
        self._setup_console_handler(root_logger)
        
        # 设置文件处理器
        if settings.LOG_FILE_ENABLED:
            self._setup_file_handlers(root_logger)
        
        # 配置特定日志器
        self._configure_specific_loggers()
        
        # 记录启动信息
        logger = logging.getLogger(__name__)
        logger.info("=" * 60)
        logger.info("🚀 Smart Scoring API 日志系统已启动")
        logger.info(f"📊 日志级别: {settings.LOG_LEVEL}")
        logger.info(f"📁 日志目录: {self.logs_dir.absolute()}")
        logger.info(f"🗂️ 文件保存: {'启用' if settings.LOG_FILE_ENABLED else '禁用'}")
        if settings.LOG_FILE_ENABLED:
            logger.info(f"⏰ 保留天数: {settings.LOG_FILE_RETENTION_DAYS} 天")
        logger.info("=" * 60)
    
    def _setup_console_handler(self, logger: logging.Logger) -> None:
        """设置控制台处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
        
        # 控制台格式 - 简洁版
        console_format = ChineseFormatter(
            fmt='%(asctime)s [%(chinese_levelname)s] %(short_name)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_format)
        
        logger.addHandler(console_handler)
    
    def _setup_file_handlers(self, logger: logging.Logger) -> None:
        """设置文件处理器"""
        # 应用日志文件
        app_log_file = self.logs_dir / "app.log"
        app_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=self._parse_size(settings.LOG_FILE_MAX_SIZE),
            backupCount=settings.LOG_FILE_BACKUP_COUNT,
            encoding='utf-8'
        )
        app_handler.setLevel(logging.INFO)
        
        # 错误日志文件
        error_log_file = self.logs_dir / "error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=self._parse_size(settings.LOG_FILE_MAX_SIZE),
            backupCount=settings.LOG_FILE_BACKUP_COUNT,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        
        # 访问日志文件
        access_log_file = self.logs_dir / "access.log"
        access_handler = logging.handlers.RotatingFileHandler(
            access_log_file,
            maxBytes=self._parse_size(settings.LOG_FILE_MAX_SIZE),
            backupCount=settings.LOG_FILE_BACKUP_COUNT,
            encoding='utf-8'
        )
        access_handler.setLevel(logging.INFO)
        
        # 文件格式 - 详细版
        file_format = ChineseFormatter(
            fmt='%(asctime)s [%(chinese_levelname)s] %(name)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        app_handler.setFormatter(file_format)
        error_handler.setFormatter(file_format)
        access_handler.setFormatter(file_format)
        
        logger.addHandler(app_handler)
        logger.addHandler(error_handler)
        
        # 为访问日志创建专门的日志器
        access_logger = logging.getLogger('access')
        access_logger.addHandler(access_handler)
        access_logger.propagate = False
    
    def _configure_specific_loggers(self) -> None:
        """配置特定的日志器"""
        # SQLAlchemy 日志配置
        sqlalchemy_logger = logging.getLogger('sqlalchemy.engine')
        if settings.DEBUG:
            sqlalchemy_logger.setLevel(logging.INFO)
        else:
            sqlalchemy_logger.setLevel(logging.WARNING)

        # Uvicorn 访问日志配置 - 启用默认访问日志
        uvicorn_access_logger = logging.getLogger('uvicorn.access')
        uvicorn_access_logger.setLevel(logging.INFO)

        # OCR 服务日志配置 - 确保显示
        ocr_logger = logging.getLogger('app.services.ocr_service')
        ocr_logger.setLevel(logging.INFO)
        ocr_logger.propagate = True  # 确保传播到根日志器

        # 评分服务日志配置 - 确保显示
        score_logger = logging.getLogger('app.services.score_service')
        score_logger.setLevel(logging.INFO)
        score_logger.propagate = True  # 确保传播到根日志器

        # API路由日志配置
        api_logger = logging.getLogger('app.api')
        api_logger.setLevel(logging.INFO)
        api_logger.propagate = True

        # 过滤开发环境的噪音日志
        logging.getLogger("watchfiles").setLevel(logging.WARNING)
        logging.getLogger("watchfiles.main").setLevel(logging.WARNING)
        logging.getLogger("multipart").setLevel(logging.WARNING)  # 过滤文件上传日志
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串为字节数"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def _cleanup_old_logs(self) -> None:
        """清理过期的日志文件"""
        if not settings.LOG_FILE_ENABLED:
            return
        
        cutoff_date = datetime.now() - timedelta(days=settings.LOG_FILE_RETENTION_DAYS)
        cleaned_count = 0
        
        try:
            for log_file in self.logs_dir.glob("*.log*"):
                if log_file.is_file():
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        log_file.unlink()
                        cleaned_count += 1
            
            if cleaned_count > 0:
                print(f"🧹 清理过期日志文件: {cleaned_count} 个")
        
        except Exception as e:
            print(f"⚠️ 清理日志文件时出错: {e}")
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            "enabled": settings.LOG_FILE_ENABLED,
            "level": settings.LOG_LEVEL,
            "retention_days": settings.LOG_FILE_RETENTION_DAYS,
            "max_file_size": settings.LOG_FILE_MAX_SIZE,
            "backup_count": settings.LOG_FILE_BACKUP_COUNT,
            "logs_directory": str(self.logs_dir.absolute()),
            "log_files": []
        }
        
        if settings.LOG_FILE_ENABLED and self.logs_dir.exists():
            for log_file in self.logs_dir.glob("*.log*"):
                if log_file.is_file():
                    file_stat = log_file.stat()
                    stats["log_files"].append({
                        "name": log_file.name,
                        "size_bytes": file_stat.st_size,
                        "size_mb": round(file_stat.st_size / 1024 / 1024, 2),
                        "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                    })
        
        return stats


# 创建全局日志配置实例
logging_config = LoggingConfig()


def setup_logging():
    """设置日志系统"""
    logging_config.setup_logging()


def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)


# 自定义访问日志中间件
class AccessLogMiddleware:
    """访问日志中间件"""
    
    def __init__(self, app):
        self.app = app
        self.access_logger = logging.getLogger('access')
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            start_time = datetime.now()

            # 获取请求信息
            method = scope["method"]
            path = scope["path"]
            client_ip = scope.get("client", ["unknown", 0])[0]

            # 处理请求
            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    status_code = message["status"]
                    end_time = datetime.now()
                    duration = (end_time - start_time).total_seconds() * 1000

                    # 记录访问日志 - 使用根日志器确保显示
                    status_text = "成功" if 200 <= status_code < 300 else "失败" if status_code >= 400 else "重定向"

                    # 同时记录到访问日志器和根日志器
                    log_message = f"{client_ip} - \"{method} {path}\" {status_code} {status_text} {duration:.1f}ms"

                    # 使用根日志器确保显示
                    root_logger = logging.getLogger()
                    root_logger.info(f"[访问] {log_message}")

                    # 也记录到专门的访问日志器
                    self.access_logger.info(log_message)

                await send(message)

            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)
