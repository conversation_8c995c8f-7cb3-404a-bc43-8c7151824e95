"""
数据库初始化脚本
创建初始用户和示例配置
"""

import logging
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.security import get_password_hash
from app.db.session import init_database, get_database_status, get_engine
from app.db.base import Base
from app.models.user import User
from app.models.weight_config import WeightConfig
from app.services import character_service

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init_db() -> None:
    """初始化数据库"""
    logger.info("🚀 开始初始化数据库...")

    # 初始化数据库连接
    db_success = init_database()
    if not db_success:
        logger.error("❌ 数据库初始化失败，无法继续")
        return

    # 获取数据库状态
    db_status = get_database_status()
    if not db_status['available']:
        logger.error("❌ 数据库不可用，无法继续")
        return

    # 获取数据库引擎
    engine = get_engine()
    if engine is None:
        logger.error("❌ 数据库引擎未正确初始化")
        return

    logger.info("✅ 数据库连接成功")

    # 创建所有表
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 数据库表创建/检查完成")
    except Exception as e:
        logger.error(f"❌ 数据库表创建失败: {e}")
        return

    # 创建数据库会话
    from app.db.session import SessionLocal
    if SessionLocal is None:
        logger.error("❌ 数据库会话工厂未初始化")
        return

    db = SessionLocal()
    try:
        # 创建管理员用户
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                role="admin",
                is_active=True
            )
            db.add(admin_user)
            logger.info("创建管理员用户: admin")
        
        # 创建普通用户
        test_user = db.query(User).filter(User.username == "testuser").first()
        if not test_user:
            test_user = User(
                username="testuser",
                email="<EMAIL>",
                hashed_password=get_password_hash("test123"),
                role="user",
                is_active=True
            )
            db.add(test_user)
            logger.info("创建测试用户: testuser")
        
        # 创建示例权重配置
        example_config = db.query(WeightConfig).filter(WeightConfig.name == "示例配置-通用").first()
        if not example_config:
            example_config = WeightConfig(
                name="示例配置-通用",
                description="这是一个示例权重配置，用于演示系统功能",
                main_props={
                    "c4": {
                        "攻击力": 0.5,
                        "暴击率": 0.8,
                        "暴击伤害": 0.9
                    },
                    "c1": {
                        "攻击力": 0.4,
                        "暴击率": 0.7
                    }
                },
                sub_props={
                    "攻击力": 1.0,
                    "暴击率": 1.2,
                    "暴击伤害": 1.1,
                    "元素精通": 0.8,
                    "充能效率": 0.6
                },
                score_max=[75.0, 80.0],
                situation_map={"c4": 0, "c1": 1},
                is_active="active"
            )
            db.add(example_config)
            logger.info("创建示例权重配置")
        
        # 创建另一个示例配置
        example_config2 = db.query(WeightConfig).filter(WeightConfig.name == "示例配置-特殊").first()
        if not example_config2:
            example_config2 = WeightConfig(
                name="示例配置-特殊",
                description="特殊情况下的权重配置",
                main_props={
                    "special": {
                        "生命值": 0.6,
                        "防御力": 0.4,
                        "治疗加成": 1.0
                    }
                },
                sub_props={
                    "生命值": 1.0,
                    "防御力": 0.8,
                    "治疗加成": 1.2,
                    "充能效率": 0.9
                },
                score_max=[60.0],
                situation_map={"special": 0},
                is_active="active"
            )
            db.add(example_config2)
            logger.info("创建特殊权重配置")
        
        db.commit()

        # 自动导入角色配置
        logger.info("开始导入角色配置...")
        try:
            imported_count, skipped_count = character_service.import_to_database(db, force_update=False)
            logger.info(f"角色配置导入完成: 导入 {imported_count} 个，跳过 {skipped_count} 个")
        except Exception as e:
            logger.error(f"角色配置导入失败: {e}")

        logger.info("数据库初始化完成")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    logger.info("开始初始化数据库...")
    init_db()
    logger.info("数据库初始化完成！")
    
    print("\n=== 初始账户信息 ===")
    print("管理员账户:")
    print("  用户名: admin")
    print("  密码: admin123")
    print("  角色: admin")
    print()
    print("测试用户账户:")
    print("  用户名: testuser")
    print("  密码: test123")
    print("  角色: user")
    print()
    print("=== 示例配置 ===")
    print("1. 示例配置-通用 (ID: 1)")
    print("   支持情境: c4, c1")
    print("2. 示例配置-特殊 (ID: 2)")
    print("   支持情境: special")
    print()
    print("现在可以启动API服务器了！")
