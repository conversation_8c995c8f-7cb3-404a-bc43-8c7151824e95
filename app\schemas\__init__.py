"""
数据模式包
统一导入所有Pydantic数据模式，便于其他模块使用
"""

from app.schemas.token import Token, TokenPayload, LoginRequest
from app.schemas.user import (
    UserBase, UserCreate, UserUpdate, UserInDB, UserResponse, UserListResponse
)
from app.schemas.weight_config import (
    WeightConfigBase, WeightConfigCreate, WeightConfigUpdate,
    WeightConfigResponse, WeightConfigListResponse
)
from app.schemas.score import (
    ScoreCalculationRequest, TermBreakdown, CalculationContext,
    OCRResult, ScoreCalculationResponse, ErrorResponse
)

# 导出所有模式
__all__ = [
    # Token schemas
    "Token", "TokenPayload", "LoginRequest",
    # User schemas
    "UserBase", "UserCreate", "UserUpdate", "UserInDB", "UserResponse", "UserListResponse",
    # Weight config schemas
    "WeightConfigBase", "WeightConfigCreate", "WeightConfigUpdate",
    "WeightConfigResponse", "WeightConfigListResponse",
    # Score schemas
    "ScoreCalculationRequest", "TermBreakdown", "CalculationContext",
    "OCRResult", "ScoreCalculationResponse", "ErrorResponse"
]
