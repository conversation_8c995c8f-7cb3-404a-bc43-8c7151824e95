# 共鸣解放伤害加成识别修复报告

## 问题描述

用户反馈OCR识别结果中包含"共鸣解放伤害加成 8.6%"，但系统没有识别到这个属性，导致评分计算中遗漏了这个重要词条。

### 原始问题表现
```json
{
  "ocr_result": {
    "raw_text": "声骸强化\n浮灵偶\nC\n+25MAX\n15100/15100\n攻击\n30.0%\n攻击\n100\n·攻击\n40\n·攻击\n7.9%\n共鸣解放伤害加成\n8.6%\n暴击伤害\n21.0%\n暴击\n9.3%"
  },
  "breakdown": [
    // 没有包含共鸣解放伤害加成的计算结果
  ],
  "invalid_terms": [
    "15100/15100",
    "%"
  ]
}
```

## 问题分析

### 根本原因
在 `app/services/ocr_service.py` 的 `_determine_final_term_name` 方法中，`always_percentage` 字典缺少对"共鸣解放伤害加成"等词条的定义，导致这些词条被当作通用情况处理，错误地添加了"%"后缀。

### 具体问题
1. OCR识别到"共鸣解放伤害加成 8.6%"
2. 解析后词条名变成"共鸣解放伤害加成%"（多了%后缀）
3. 配置文件中的键是"共鸣解放伤害加成"（无%后缀）
4. 键名不匹配，导致无法找到对应权重，词条被忽略

## 修复方案

### 1. 扩展always_percentage字典
在 `_determine_final_term_name` 方法中添加缺失的词条定义：

```python
# 始终为百分比的词条
always_percentage = {
    "暴击": "暴击",
    "暴击率": "暴击率", 
    "暴击伤害": "暴击伤害",
    "元素精通": "元素精通",
    "充能效率": "充能效率",
    "治疗加成": "治疗加成",
    "治疗效果加成": "治疗效果加成",        # 新增
    "普攻伤害加成": "普攻伤害加成",
    "重击伤害加成": "重击伤害加成",
    "共鸣技能伤害加成": "共鸣技能伤害加成",  # 新增
    "共鸣解放伤害加成": "共鸣解放伤害加成",  # 新增 - 关键修复
    "元素战技伤害加成": "元素战技伤害加成",
    "元素爆发伤害加成": "元素爆发伤害加成",
    "共鸣效率": "共鸣效率",              # 新增
    "属性伤害加成": "属性伤害加成"        # 新增
}
```

### 2. 修复词条名称清理逻辑
移除对"属性"前缀的清理，因为"属性伤害加成"是完整的词条名：

```python
# 修改前
prefixes_to_remove = ['属性', '数值', '值', '：', ':', '等级', '+']

# 修改后  
prefixes_to_remove = ['数值', '值', '：', ':', '等级', '+']
```

## 修复验证

### 测试用例
测试了以下词条的识别：

| 词条名 | 测试文本 | 识别结果 | 状态 |
|--------|----------|----------|------|
| 共鸣解放伤害加成 | "共鸣解放伤害加成 8.6%" | ✅ 正确识别 | 通过 |
| 共鸣技能伤害加成 | "共鸣技能伤害加成 15.2%" | ✅ 正确识别 | 通过 |
| 普攻伤害加成 | "普攻伤害加成 12.3%" | ✅ 正确识别 | 通过 |
| 重击伤害加成 | "重击伤害加成 9.8%" | ✅ 正确识别 | 通过 |
| 共鸣效率 | "共鸣效率 25.4%" | ✅ 正确识别 | 通过 |
| 属性伤害加成 | "属性伤害加成 18.7%" | ✅ 正确识别 | 通过 |
| 治疗效果加成 | "治疗效果加成 22.1%" | ✅ 正确识别 | 通过 |

### 验证结果
- ✅ 所有测试用例通过
- ✅ 共鸣解放伤害加成现在可以正确识别
- ✅ 其他相关词条也得到了修复

## 影响范围

### 受益的词条类型
修复后，以下类型的词条现在都能正确识别：
- 共鸣相关：共鸣解放伤害加成、共鸣技能伤害加成、共鸣效率
- 伤害加成：普攻伤害加成、重击伤害加成、属性伤害加成
- 治疗相关：治疗效果加成

### 兼容性
- ✅ 向后兼容，不影响现有功能
- ✅ 不影响其他词条的识别
- ✅ 保持原有的解析优先级

## 配置文件验证

### 布兰特配置确认
在 `character/布兰特/calc.json` 中确认包含：
```json
{
  "sub_props": {
    "共鸣解放伤害加成": 0.165,
    // 其他词条...
  }
}
```

权重值为 0.165，说明这是一个重要的评分词条。

## 预期效果

修复后，使用相同的OCR结果重新计算评分，应该会看到：

1. **breakdown中新增词条**：
```json
{
  "term_name": "共鸣解放伤害加成",
  "term_type": "sub_prop", 
  "source_text": "共鸣解放伤害加成 8.6%",
  "extracted_value": 8.6,
  "weight": 0.165,
  "score": 计算得分
}
```

2. **总分提升**：由于新增了一个有权重的词条，总分应该会有所提升

3. **valid_terms_count增加**：有效词条数量会增加1

## 总结

通过扩展OCR服务中的词条识别字典，成功修复了"共鸣解放伤害加成"等词条无法识别的问题。这个修复不仅解决了用户反馈的具体问题，还提升了系统对各种伤害加成类词条的识别能力，提高了评分计算的准确性。

### 修复文件
- `app/services/ocr_service.py` - 主要修复文件

### 测试状态
- ✅ 单元测试通过
- ✅ 集成测试验证
- ✅ 兼容性确认
