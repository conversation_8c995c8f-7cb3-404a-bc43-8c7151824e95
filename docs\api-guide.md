# Smart Scoring API 使用指南

本文档详细介绍了如何使用 Smart Scoring API 的各个功能。

## API 概览

Smart Scoring API 提供以下主要功能：

- **用户认证**: 登录、令牌验证
- **权重配置管理**: 创建、查询、更新、删除权重配置（管理员）
- **评分计算**: 上传图片进行OCR识别和评分计算

## 基础信息

- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: Bearer Token (JWT)
- **数据格式**: JSON
- **文件上传**: multipart/form-data

## 认证流程

### 1. 用户登录

**端点**: `POST /auth/login-json`

**请求示例**:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login-json" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### 2. 使用令牌

在后续请求中，需要在请求头中包含认证令牌：

```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  "http://localhost:8000/api/v1/auth/me"
```

## 权重配置管理

### 1. 创建权重配置

**端点**: `POST /configs/`
**权限**: 仅管理员

**请求示例**:
```bash
curl -X POST "http://localhost:8000/api/v1/configs/" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "角色A-通用配置",
    "description": "适用于角色A的通用权重配置",
    "main_props": {
      "c4": {
        "攻击力": 0.5,
        "暴击率": 0.8,
        "暴击伤害": 0.9
      },
      "c1": {
        "攻击力": 0.4,
        "暴击率": 0.7
      }
    },
    "sub_props": {
      "攻击力": 1.0,
      "暴击率": 1.2,
      "暴击伤害": 1.1,
      "元素精通": 0.8,
      "充能效率": 0.6
    },
    "score_max": [75.0, 80.0],
    "situation_map": {
      "c4": 0,
      "c1": 1
    }
  }'
```

**配置说明**:
- `main_props`: 主词条权重，按情境分组
- `sub_props`: 副词条权重，所有情境通用
- `score_max`: 各情境的未对齐最高分
- `situation_map`: 情境到score_max索引的映射

### 2. 查询权重配置

**获取配置列表**:
```bash
curl -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/configs/?skip=0&limit=10"
```

**获取单个配置**:
```bash
curl -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/configs/1"
```

### 3. 更新权重配置

```bash
curl -X PUT "http://localhost:8000/api/v1/configs/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的配置名称",
    "description": "更新后的描述"
  }'
```

### 4. 激活/停用配置

**激活配置**:
```bash
curl -X POST "http://localhost:8000/api/v1/configs/1/activate" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**停用配置**:
```bash
curl -X POST "http://localhost:8000/api/v1/configs/1/deactivate" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 评分计算

### 1. 图片评分

**端点**: `POST /scores/calculate`
**权限**: 认证用户

**请求示例**:
```bash
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@/path/to/image.png" \
  -F "config_id=1" \
  -F "situation=c4"
```

**响应示例**:
```json
{
  "total_score": 52.75,
  "calculation_context": {
    "config_name": "角色A-通用配置",
    "config_id": 1,
    "situation": "c4",
    "max_unaligned_score": 75.0,
    "aligned_score": 50.0
  },
  "breakdown": [
    {
      "term_name": "攻击力",
      "term_type": "main_prop",
      "source_text": "攻击力: 30%",
      "extracted_value": 0.3,
      "weight": 0.5,
      "score": 10.0
    },
    {
      "term_name": "暴击率",
      "term_type": "sub_prop",
      "source_text": "暴击率数值25%",
      "extracted_value": 0.25,
      "weight": 1.2,
      "score": 20.0
    }
  ],
  "ocr_result": {
    "raw_text": "攻击力: 30%\n暴击率数值25%\n暴击伤害: 40%",
    "confidence": 0.95,
    "processing_time": 1.23
  },
  "valid_terms_count": 2,
  "invalid_terms": []
}
```

### 2. 获取配置支持的情境

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/scores/configs/1/situations"
```

**响应示例**:
```json
{
  "config_id": 1,
  "situations": ["c4", "c1"]
}
```

### 3. 预览计算（管理员）

**端点**: `POST /scores/preview`
**权限**: 仅管理员

```bash
curl -X POST "http://localhost:8000/api/v1/scores/preview" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "config_id=1" \
  -F "situation=c4" \
  -F 'mock_terms=[
    {
      "term_name": "攻击力",
      "source_text": "攻击力: 30%",
      "extracted_value": 0.3
    },
    {
      "term_name": "暴击率",
      "source_text": "暴击率: 25%",
      "extracted_value": 0.25
    }
  ]'
```

## 评分计算公式

系统使用以下公式计算词条得分：

```
词条得分 = (词条数值 × 当前词条权重 ÷ 当前情境的未对齐最高分) × 对齐分数(50)
```

**示例计算**:
- 词条: 攻击力 30%
- 权重: 0.5
- 未对齐最高分: 75.0
- 对齐分数: 50.0

```
词条得分 = (0.3 × 0.5 ÷ 75.0) × 50.0 = 0.1
```

## 错误处理

### 常见错误码

- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 认证失败
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `422 Unprocessable Entity`: 数据验证失败
- `500 Internal Server Error`: 服务器内部错误

### 错误响应格式

```json
{
  "error": "ValidationError",
  "message": "配置ID不存在",
  "details": {
    "config_id": 999
  }
}
```

## 最佳实践

### 1. 图片要求

- **格式**: JPEG, PNG
- **大小**: 不超过 10MB
- **分辨率**: 建议 800x600 以上
- **清晰度**: 文字清晰可读

### 2. 权重配置设计

- **主词条**: 针对特定情境的核心属性
- **副词条**: 通用属性，所有情境共享
- **权重值**: 建议范围 0.1-2.0
- **最高分**: 根据实际需求设定基准值

### 3. 性能优化

- **缓存**: 频繁使用的配置会被缓存
- **批量处理**: 避免频繁的单个请求
- **图片优化**: 压缩图片以提高上传速度

### 4. 安全建议

- **令牌管理**: 定期刷新访问令牌
- **权限控制**: 严格区分管理员和普通用户权限
- **数据验证**: 客户端也应进行基本的数据验证

## SDK 和工具

### Python SDK 示例

```python
import requests
import json

class SmartScoringClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {token}"}
    
    def calculate_score(self, image_path, config_id, situation):
        with open(image_path, 'rb') as f:
            files = {"file": f}
            data = {"config_id": config_id, "situation": situation}
            
            response = requests.post(
                f"{self.base_url}/scores/calculate",
                files=files,
                data=data,
                headers=self.headers
            )
            return response.json()

# 使用示例
client = SmartScoringClient("http://localhost:8000/api/v1", "your_token")
result = client.calculate_score("image.png", 1, "c4")
print(f"总分: {result['total_score']}")
```

### JavaScript SDK 示例

```javascript
class SmartScoringClient {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`
        };
    }
    
    async calculateScore(file, configId, situation) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('config_id', configId);
        formData.append('situation', situation);
        
        const response = await fetch(`${this.baseUrl}/scores/calculate`, {
            method: 'POST',
            headers: this.headers,
            body: formData
        });
        
        return await response.json();
    }
}

// 使用示例
const client = new SmartScoringClient('http://localhost:8000/api/v1', 'your_token');
const result = await client.calculateScore(fileInput.files[0], 1, 'c4');
console.log(`总分: ${result.total_score}`);
```

## 支持和反馈

如果您在使用过程中遇到问题，请：

1. 查看 [故障排除文档](troubleshooting.md)
2. 检查 [API 文档](http://localhost:8000/api/v1/docs)
3. 提交 [GitHub Issue](https://github.com/your-org/smart-scoring-api/issues)
4. 联系技术支持: <EMAIL>
