# Smart Scoring API

智能评分API系统 - 基于OCR的图片评分后端服务

## 项目简介

Smart Scoring API 是一个基于 FastAPI 和 PaddleOCR 的智能评分系统，能够从图片中提取文字信息并根据预设的权重配置计算评分。系统支持复杂的权重配置管理，适用于游戏装备评分、考试评分等多种场景。

## 主要特性

- 🚀 **高性能**: 基于 FastAPI 异步框架，支持高并发请求
- 🔒 **安全认证**: JWT 令牌认证，支持管理员和普通用户角色
- 🎯 **智能OCR**: 集成 PaddleOCR，支持中英文图片文字识别
- ⚙️ **灵活配置**: 支持复杂的权重配置管理，适应不同评分场景
- 🎮 **角色配置**: 自动读取character目录下的角色配置文件，支持39+个角色
- 📊 **详细分析**: 提供详细的评分明细和计算上下文
- 🐳 **容器化**: 完整的 Docker 部署方案
- 📚 **完整文档**: 自动生成的 API 文档和详细的使用说明

## 技术栈

- **后端框架**: FastAPI 0.104+
- **数据库**: PostgreSQL 15
- **缓存**: Redis 7
- **OCR引擎**: PaddleOCR 2.7+
- **认证**: JWT (python-jose)
- **数据验证**: Pydantic 2.5+
- **ORM**: SQLAlchemy 2.0+
- **容器化**: Docker & Docker Compose

## 快速开始

### 方式一：Docker Compose（推荐）

1. 克隆项目
```bash
git clone <repository-url>
cd smart-scoring-api
```

2. 复制环境配置文件
```bash
cp .env.example .env
```

3. 启动服务
```bash
# 生产环境
docker-compose up -d

# 开发环境
docker-compose -f docker-compose.dev.yml up -d
```

4. 初始化数据库（如果需要）
```bash
docker-compose exec api python init_db.py
```

5. 访问API文档
- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc

### 方式二：本地开发

1. 安装依赖
```bash
# 使用 Poetry（推荐）
poetry install

# 或使用 pip
pip install -r requirements.txt
```

2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库等信息
```

3. 启动数据库服务
```bash
docker-compose up -d db redis
```

4. 初始化数据库
```bash
python init_db.py
```

5. 启动应用
```bash
# 使用 Poetry
poetry run python main.py

# 或直接运行
python main.py
```

## API 使用示例

### 1. 用户认证

```bash
# 登录获取令牌
curl -X POST "http://localhost:8000/api/v1/auth/login-json" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### 2. 创建权重配置（管理员）

```bash
curl -X POST "http://localhost:8000/api/v1/configs/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "示例配置",
    "main_props": {
      "c4": {"攻击力": 0.5, "暴击率": 0.8}
    },
    "sub_props": {
      "攻击力": 1.0, "暴击率": 1.2
    },
    "score_max": [75.0],
    "situation_map": {"c4": 0}
  }'
```

### 3. 角色配置管理

```bash
# 获取所有角色列表
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/characters/list"

# 获取指定角色的配置
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/characters/今汐/config"

# 导入所有角色配置到数据库（管理员）
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/characters/import"
```

### 4. 图片评分计算

```bash
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test_image.png" \
  -F "config_id=1" \
  -F "situation=c4"
```

## 项目结构

```
smart-scoring-api/
├── app/                      # 主要应用代码
│   ├── api/                  # API路由和端点
│   ├── core/                 # 核心配置和安全
│   ├── crud/                 # 数据库操作
│   ├── db/                   # 数据库连接
│   ├── models/               # 数据模型
│   ├── schemas/              # 数据验证模式
│   └── services/             # 业务逻辑服务
├── tests/                    # 测试代码
├── docs/                     # 项目文档
├── docker-compose.yml        # Docker编排文件
├── Dockerfile               # Docker镜像构建
├── main.py                  # 应用入口
└── init_db.py              # 数据库初始化
```

## 配置说明

主要配置项（.env 文件）：

```env
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/dbname

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OCR配置
OCR_USE_GPU=false
OCR_LANG=ch

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
```

## 测试

```bash
# 运行单元测试
pytest

# 运行手动API测试
python test_api_manual.py

# 生成测试覆盖率报告
pytest --cov=app --cov-report=html
```

## 部署

### 生产环境部署

1. 配置环境变量
2. 使用 HTTPS（配置 SSL 证书）
3. 配置反向代理（Nginx）
4. 设置日志收集
5. 配置监控和告警

### 性能优化

- 使用 Redis 缓存权重配置
- 配置数据库连接池
- 启用 Gzip 压缩
- 使用 CDN 加速静态资源

## 故障排除

### 常见问题

1. **OCR识别失败**
   - 检查图片格式和大小
   - 确认 PaddleOCR 依赖安装正确

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接字符串配置

3. **权限错误**
   - 确认用户角色正确
   - 检查 JWT 令牌有效性

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: Smart Scoring Team
- 邮箱: <EMAIL>
- 问题反馈: [GitHub Issues](https://github.com/your-org/smart-scoring-api/issues)

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础OCR评分功能
- 权重配置管理
- Docker部署支持
