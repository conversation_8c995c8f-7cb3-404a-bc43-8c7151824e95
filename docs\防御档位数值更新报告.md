# 防御档位数值更新报告

## 📋 更新概述

根据最新的游戏数据，本次更新对防御相关的词条档位进行了调整：
1. **防御固定值**：新增70档位
2. **防御百分比**：最高档位从15.0%修改为14.7%

## 🎯 更新详情

### 1. 防御固定值档位扩展

#### 更新前
```python
'防御': [40, 50, 60]  # 3个档位
```

#### 更新后
```python
'防御': [40, 50, 60, 70]  # 4个档位，新增70
```

### 2. 防御百分比最高档位调整

#### 更新前
```python
'防御%': [8.10, 9.0, 10.0, 10.90, 11.80, 12.80, 13.80, 15.0]  # 最高15.0%
```

#### 更新后
```python
'防御%': [8.10, 9.0, 10.0, 10.90, 11.80, 12.80, 13.80, 14.7]  # 最高14.7%
```

## 🔧 代码修改详情

### 1. OCR服务更新

#### 文件：`app/services/ocr_service.py`

**位置1：第922行 - 防御固定值档位**
```python
# 修改前
'防御': [40, 50, 60] if not is_percentage else None,

# 修改后
'防御': [40, 50, 60, 70] if not is_percentage else None,
```

**位置2：第928行 - 防御百分比档位**
```python
# 修改前
'防御%': [8.10, 9.0, 10.0, 10.90, 11.80, 12.80, 13.80, 15.0] if is_percentage else None,

# 修改后
'防御%': [8.10, 9.0, 10.0, 10.90, 11.80, 12.80, 13.80, 14.7] if is_percentage else None,
```

### 2. 分数计算服务更新

#### 文件：`app/services/score_service.py`

**位置1：第244行 - 词条类型判断中的防御固定值**
```python
# 修改前
"防御": [40, 50, 60],  # 固定值

# 修改后
"防御": [40, 50, 60, 70],  # 固定值
```

**位置2：第247行 - 词条类型判断中的防御百分比**
```python
# 修改前
"防御%": [8.1, 9.0, 10.0, 10.9, 11.8, 12.8, 13.8, 15.0],  # 百分比

# 修改后
"防御%": [8.1, 9.0, 10.0, 10.9, 11.8, 12.8, 13.8, 14.7],  # 百分比
```

**位置3：第639行 - 词条验证中的防御固定值**
```python
# 修改前
"防御": [60, 50, 40],

# 修改后
"防御": [70, 60, 50, 40],
```

**位置4：第638行 - 词条验证中的防御百分比**
```python
# 修改前
"防御%": [15.0, 13.8, 12.8, 11.8, 10.9, 10.0, 9.0, 8.1],

# 修改后
"防御%": [14.7, 13.8, 12.8, 11.8, 10.9, 10.0, 9.0, 8.1],
```

## 📊 影响分析

### 1. 防御固定值70档位

#### 新增支持的情况
- ✅ **OCR识别**：现在可以识别"防御 70"、"防御 70-"等格式
- ✅ **智能推断**：单独的"防御"和"70"可以被正确配对
- ✅ **分数计算**：防御70档位现在可以正确计入分数计算
- ✅ **验证通过**：防御70数值可以通过词条验证

#### 实际应用
```python
# 示例1：直接识别
"防御 70" → 解析成功 → 验证通过 → 计入分数

# 示例2：带符号识别
"防御 70-" → 解析成功 → 验证通过 → 计入分数

# 示例3：分离配对
"防御" + "70" → 智能配对成功 → 验证通过 → 计入分数
```

### 2. 防御百分比14.7%档位

#### 数值精度调整
- ✅ **更准确的数值**：从15.0%调整为14.7%，更符合游戏实际数据
- ✅ **向下兼容**：原有的14.7%以下档位保持不变
- ✅ **验证更严格**：15.0%现在会被识别为无效数值

#### 实际应用
```python
# 示例1：最高档位
"防御% 14.7%" → 解析成功 → 验证通过 → 计入分数

# 示例2：原15.0%现在无效
"防御% 15.0%" → 解析成功 → 验证失败 → 不计入分数

# 示例3：其他档位不受影响
"防御% 13.8%" → 解析成功 → 验证通过 → 计入分数
```

## ✅ 验证结果

### 数据一致性检查
- ✅ **OCR服务**：防御档位已更新
- ✅ **分数计算服务**：所有相关方法已同步更新
- ✅ **词条类型判断**：`_determine_term_type_by_value`已更新
- ✅ **词条验证**：`_validate_term_value`已更新
- ✅ **智能推断**：`_select_best_term_match`已更新

### 功能完整性检查
- ✅ **解析能力**：支持防御70和防御%14.7%的识别
- ✅ **验证逻辑**：新档位可以通过验证
- ✅ **分数计算**：新档位可以正确计入分数
- ✅ **智能配对**：分离的词条和数值可以正确配对

## 🎉 总结

本次防御档位更新成功完成：

### 主要成果
1. **扩展了防御固定值档位**：从3个档位扩展到4个档位
2. **精确了防御百分比数值**：最高档位从15.0%调整为14.7%
3. **保持了系统一致性**：所有相关代码都已同步更新
4. **增强了识别能力**：现在可以正确处理更多的防御词条情况

### 用户体验提升
- **更准确的识别**：防御70档位现在可以被正确识别和计分
- **更精确的验证**：防御百分比数值更符合游戏实际情况
- **更好的兼容性**：支持各种OCR识别格式和符号

这些更新确保了系统能够准确处理最新的游戏数据，为用户提供更精确的词条识别和评分服务。
