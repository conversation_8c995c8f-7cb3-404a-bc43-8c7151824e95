<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base64图片上传演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        
        .upload-section.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        input[type="file"] {
            margin: 10px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, select, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            margin-top: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .preview {
            margin-top: 20px;
            text-align: center;
        }
        
        .preview img {
            max-width: 100%;
            max-height: 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            text-align: center;
            color: #007bff;
            font-weight: bold;
        }
        
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Base64图片上传演示</h1>
        
        <div class="info">
            <strong>功能说明：</strong>
            <ul>
                <li>支持拖拽上传或点击选择图片</li>
                <li>自动转换为Base64格式</li>
                <li>支持JPEG、PNG、GIF、BMP、WebP格式</li>
                <li>可选择配置ID或角色名称</li>
                <li>自动判断声骸情况或手动指定</li>
            </ul>
        </div>
        
        <div class="upload-section" id="uploadSection">
            <p>📁 拖拽图片到此处或点击选择文件</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>
        
        <div class="preview" id="preview" style="display: none;">
            <img id="previewImg" alt="预览图片">
            <p id="imageInfo"></p>
        </div>
        
        <form id="uploadForm">
            <div class="form-group">
                <label for="apiUrl">API地址:</label>
                <input type="url" id="apiUrl" value="http://localhost:8000/api/v1/scores/calculate-base64" required>
            </div>
            
            <div class="form-group">
                <label for="token">认证令牌:</label>
                <input type="text" id="token" placeholder="Bearer token (可选，如果API需要认证)">
            </div>
            
            <div class="form-group">
                <label for="configType">配置方式:</label>
                <select id="configType">
                    <option value="config_id">使用配置ID</option>
                    <option value="character_name">使用角色名称</option>
                </select>
            </div>
            
            <div class="form-group" id="configIdGroup">
                <label for="configId">配置ID:</label>
                <input type="number" id="configId" value="43" min="1">
            </div>
            
            <div class="form-group" id="characterNameGroup" style="display: none;">
                <label for="characterName">角色名称:</label>
                <input type="text" id="characterName" placeholder="例如: 布兰特">
            </div>
            
            <div class="form-group">
                <label for="situation">情况 (可选):</label>
                <select id="situation">
                    <option value="">自动判断</option>
                    <option value="4">情况4 (COST4)</option>
                    <option value="3">情况3 (COST3)</option>
                    <option value="1">情况1 (COST1)</option>
                </select>
            </div>
            
            <button type="submit" id="submitBtn" disabled>🚀 开始评分</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        let selectedFile = null;
        let base64Data = null;

        // DOM元素
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('fileInput');
        const preview = document.getElementById('preview');
        const previewImg = document.getElementById('previewImg');
        const imageInfo = document.getElementById('imageInfo');
        const uploadForm = document.getElementById('uploadForm');
        const submitBtn = document.getElementById('submitBtn');
        const result = document.getElementById('result');
        const configType = document.getElementById('configType');
        const configIdGroup = document.getElementById('configIdGroup');
        const characterNameGroup = document.getElementById('characterNameGroup');

        // 配置类型切换
        configType.addEventListener('change', function() {
            if (this.value === 'config_id') {
                configIdGroup.style.display = 'block';
                characterNameGroup.style.display = 'none';
            } else {
                configIdGroup.style.display = 'none';
                characterNameGroup.style.display = 'block';
            }
        });

        // 拖拽上传
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // 点击上传
        uploadSection.addEventListener('click', function() {
            fileInput.click();
        });

        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 处理文件
        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件！');
                return;
            }

            selectedFile = file;
            
            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                base64Data = e.target.result;
                previewImg.src = base64Data;
                imageInfo.textContent = `文件名: ${file.name} | 大小: ${(file.size / 1024).toFixed(1)} KB | 类型: ${file.type}`;
                preview.style.display = 'block';
                submitBtn.disabled = false;
            };
            reader.readAsDataURL(file);
        }

        // 表单提交
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!base64Data) {
                alert('请先选择图片！');
                return;
            }

            const apiUrl = document.getElementById('apiUrl').value;
            const token = document.getElementById('token').value;
            const configTypeValue = configType.value;
            const situation = document.getElementById('situation').value;

            // 构建请求数据
            const requestData = {
                image_data: base64Data,
                situation: situation || undefined
            };

            if (configTypeValue === 'config_id') {
                const configId = parseInt(document.getElementById('configId').value);
                if (configId) {
                    requestData.config_id = configId;
                }
            } else {
                const characterName = document.getElementById('characterName').value;
                if (characterName) {
                    requestData.character_name = characterName;
                }
            }

            // 构建请求头
            const headers = {
                'Content-Type': 'application/json'
            };

            if (token) {
                headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
            }

            // 显示加载状态
            result.style.display = 'block';
            result.className = 'result loading';
            result.textContent = '🔄 正在处理图片，请稍候...';
            submitBtn.disabled = true;

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestData)
                });

                const responseData = await response.json();

                if (response.ok) {
                    result.className = 'result success';
                    result.textContent = `✅ 评分成功！\n\n${JSON.stringify(responseData, null, 2)}`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ 评分失败！\n\n错误信息: ${responseData.detail || '未知错误'}\n\n完整响应:\n${JSON.stringify(responseData, null, 2)}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 请求失败！\n\n错误信息: ${error.message}\n\n请检查:\n1. API地址是否正确\n2. 服务是否正在运行\n3. 网络连接是否正常`;
            } finally {
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
