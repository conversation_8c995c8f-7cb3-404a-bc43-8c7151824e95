"""
权重配置CRUD操作
实现权重配置相关的数据库操作
"""

from typing import List, Optional

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.weight_config import WeightConfig
from app.schemas.weight_config import WeightConfigCreate, WeightConfigUpdate


class CRUDWeightConfig(CRUDBase[WeightConfig, WeightConfigCreate, WeightConfigUpdate]):
    """权重配置CRUD操作类"""
    
    def get_by_name(self, db: Session, *, name: str) -> Optional[WeightConfig]:
        """
        根据配置名称获取权重配置
        
        Args:
            db: 数据库会话
            name: 配置名称
            
        Returns:
            权重配置对象或None
        """
        return db.query(WeightConfig).filter(WeightConfig.name == name).first()
    
    def get_active_configs(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[WeightConfig]:
        """
        获取激活的权重配置列表
        
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            激活的权重配置列表
        """
        return (
            db.query(WeightConfig)
            .filter(WeightConfig.is_active == "active")
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_status(
        self, db: Session, *, status: str, skip: int = 0, limit: int = 100
    ) -> List[WeightConfig]:
        """
        根据状态获取权重配置列表
        
        Args:
            db: 数据库会话
            status: 配置状态 (active/inactive)
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            指定状态的权重配置列表
        """
        return (
            db.query(WeightConfig)
            .filter(WeightConfig.is_active == status)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def activate_config(self, db: Session, *, config_id: int) -> Optional[WeightConfig]:
        """
        激活权重配置
        
        Args:
            db: 数据库会话
            config_id: 配置ID
            
        Returns:
            更新后的权重配置对象或None
        """
        config = self.get(db, id=config_id)
        if config:
            config.is_active = "active"
            db.add(config)
            db.commit()
            db.refresh(config)
        return config
    
    def deactivate_config(self, db: Session, *, config_id: int) -> Optional[WeightConfig]:
        """
        停用权重配置
        
        Args:
            db: 数据库会话
            config_id: 配置ID
            
        Returns:
            更新后的权重配置对象或None
        """
        config = self.get(db, id=config_id)
        if config:
            config.is_active = "inactive"
            db.add(config)
            db.commit()
            db.refresh(config)
        return config
    
    def search_by_name(
        self, db: Session, *, name_pattern: str, skip: int = 0, limit: int = 100
    ) -> List[WeightConfig]:
        """
        根据名称模式搜索权重配置
        
        Args:
            db: 数据库会话
            name_pattern: 名称模式（支持模糊匹配）
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            匹配的权重配置列表
        """
        return (
            db.query(WeightConfig)
            .filter(WeightConfig.name.ilike(f"%{name_pattern}%"))
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def validate_situation_exists(
        self, db: Session, *, config_id: int, situation: str
    ) -> bool:
        """
        验证指定配置中是否存在指定情境
        
        Args:
            db: 数据库会话
            config_id: 配置ID
            situation: 情境标识符
            
        Returns:
            是否存在指定情境
        """
        config = self.get(db, id=config_id)
        if not config:
            return False
        
        return situation in config.situation_map
    
    def get_situation_score_max(
        self, db: Session, *, config_id: int, situation: str
    ) -> Optional[float]:
        """
        获取指定配置和情境的最高分
        
        Args:
            db: 数据库会话
            config_id: 配置ID
            situation: 情境标识符
            
        Returns:
            最高分或None
        """
        config = self.get(db, id=config_id)
        if not config or situation not in config.situation_map:
            return None
        
        index = config.situation_map[situation]
        if index >= len(config.score_max):
            return None
        
        return config.score_max[index]
    
    def count_active(self, db: Session) -> int:
        """
        获取激活配置的总数
        
        Args:
            db: 数据库会话
            
        Returns:
            激活配置总数
        """
        return db.query(WeightConfig).filter(WeightConfig.is_active == "active").count()


# 创建权重配置CRUD实例
weight_config = CRUDWeightConfig(WeightConfig)
