"""
用户数据模型
定义用户表结构和相关字段
"""

from sqlalchemy import <PERSON>olean, Column, DateTime, Integer, String
from sqlalchemy.sql import func

from app.db.base import Base


class User(Base):
    """用户模型"""
    
    __tablename__ = "users"
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True)
    
    # 用户名（唯一）
    username = Column(String(50), unique=True, index=True, nullable=False)
    
    # 邮箱（唯一，可选）
    email = Column(String(100), unique=True, index=True, nullable=True)
    
    # 密码哈希
    hashed_password = Column(String(255), nullable=False)
    
    # 用户角色：admin 或 user
    role = Column(String(20), default="user", nullable=False)
    
    # 是否激活
    is_active = Column(Boolean, default=True, nullable=False)
    
    # 创建时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 更新时间
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"
