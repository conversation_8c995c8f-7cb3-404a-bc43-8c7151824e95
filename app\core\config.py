"""
应用配置管理
使用Pydantic Settings从环境变量加载配置
"""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用设置类"""
    
    # 项目基本信息
    PROJECT_NAME: str = Field(default="Smart Scoring API", description="项目名称")
    VERSION: str = Field(default="1.0.0", description="版本号")
    DEBUG: bool = Field(default=False, description="调试模式")
    API_V1_STR: str = Field(default="/api/v1", description="API v1前缀")
    
    # 数据库配置
    DATABASE_URL: Optional[str] = Field(
        default=None,
        description="数据库连接URL (可选，未配置时将禁用数据库功能)"
    )
    
    # Redis配置
    REDIS_URL: str = Field(
        default="redis://localhost:6379/0",
        description="Redis连接URL"
    )
    
    # JWT安全配置
    SECRET_KEY: str = Field(
        default="your-super-secret-jwt-key-change-this-in-production",
        description="JWT密钥"
    )
    ALGORITHM: str = Field(default="HS256", description="JWT算法")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="访问令牌过期时间（分钟）")
    
    # 文件上传配置
    MAX_FILE_SIZE: int = Field(default=10485760, description="最大文件大小（字节）")  # 10MB
    ALLOWED_FILE_TYPES: str = Field(
        default="image/jpeg,image/png,image/jpg,image/mpo",
        description="允许的文件类型（逗号分隔）"
    )
    
    # OCR配置
    OCR_USE_GPU: bool = Field(default=False, description="OCR是否使用GPU")
    OCR_LANG: str = Field(default="ch", description="OCR语言")
    
    # CORS配置
    CORS_ORIGINS: str = Field(
        default="http://localhost:3000,http://localhost:8080",
        description="允许的CORS源（逗号分隔）"
    )
    
    # 缓存配置
    CACHE_EXPIRE_SECONDS: int = Field(default=3600, description="缓存过期时间（秒）")

    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE_ENABLED: bool = Field(default=True, description="是否启用日志文件保存")
    LOG_FILE_RETENTION_DAYS: int = Field(default=7, description="日志文件保留天数")
    LOG_FILE_MAX_SIZE: str = Field(default="10MB", description="单个日志文件最大大小")
    LOG_FILE_BACKUP_COUNT: int = Field(default=5, description="日志文件备份数量")

    @property
    def allowed_file_types_list(self) -> List[str]:
        """获取允许的文件类型列表"""
        return [ft.strip() for ft in self.ALLOWED_FILE_TYPES.split(",")]

    @property
    def cors_origins_list(self) -> List[str]:
        """获取允许的CORS源列表"""
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8-sig"
        case_sensitive = True


# 创建全局设置实例
settings = Settings()
