# 角色别名配置使用指南

## 功能概述

Smart Scoring API 现在支持角色别名功能，您可以使用多种别名来代替完整的角色名进行评分计算。

## 配置文件

### 位置
- 文件名: `character_aliases.json`
- 位置: 项目根目录

### 格式
```json
{
  "aliases": {
    "角色名": ["别名1", "别名2", "别名3"],
    "卡提希娅": [
      "卡提希娅",
      "卡提",
      "卡卡",
      "katishiya",
      "Katishiya",
      "KATISHIYA"
    ]
  }
}
```

### 规则
1. **角色名**: 必须与 `character` 目录下的文件夹名完全一致
2. **别名**: 不区分大小写，支持中文、英文、拼音等
3. **唯一性**: 每个别名只能对应一个角色
4. **包含原名**: 建议在别名列表中包含角色的原名

## 预配置的别名

系统已预配置了常用角色的别名：

### 主要角色
- **卡提希娅**: 卡提, 卡卡, katishiya, Katishiya, KATISHIYA
- **今汐**: 汐汐, jinxi, Jinxi, JINXI
- **安可**: 安安, encore, Encore, ENCORE
- **长离**: 离离, changli, Changli, CHANGLI
- **维里奈**: 维里, 奈奈, verina, Verina, VERINA

### 其他角色
- **炽霞**: 霞霞, chixia, Chixia, CHIXIA
- **丹瑾**: 瑾瑾, danjin, Danjin, DANJIN
- **凌阳**: 阳阳, lingyang, Lingyang, LINGYANG
- **卡卡罗**: 罗罗, kakaro, Kakaro, KAKARO
- **吟霖**: 霖霖, yinlin, Yinlin, YINLIN

## API 使用

### 1. 获取别名配置

**端点**: `GET /api/v1/characters/aliases`

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/characters/aliases"
```

**响应示例**:
```json
{
  "aliases_by_character": {
    "卡提希娅": ["卡提希娅", "卡提", "卡卡", "katishiya"],
    "今汐": ["今汐", "汐汐", "jinxi"]
  },
  "total_aliases": 25,
  "total_characters": 10,
  "usage": "可以使用任何别名来代替角色名进行评分计算"
}
```

### 2. 使用别名进行评分

**端点**: `POST /api/v1/scores/calculate`

```bash
# 使用原名
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.png" \
  -F "character_name=卡提希娅" \
  -F "situation=4"

# 使用中文简称
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.png" \
  -F "character_name=卡提" \
  -F "situation=4"

# 使用昵称
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.png" \
  -F "character_name=卡卡" \
  -F "situation=4"

# 使用英文别名
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.png" \
  -F "character_name=katishiya" \
  -F "situation=4"
```

## 自定义别名

### 添加新别名

1. **编辑配置文件**:
   ```bash
   # 编辑 character_aliases.json
   notepad character_aliases.json
   ```

2. **添加别名**:
   ```json
   {
     "aliases": {
       "卡提希娅": [
         "卡提希娅",
         "卡提",
         "卡卡",
         "katishiya",
         "小卡",      // 新增昵称
         "KT",        // 新增缩写
         "卡老师"     // 新增称呼
       ]
     }
   }
   ```

3. **重启服务**:
   ```bash
   # 重启API服务以加载新配置
   python main.py
   ```

### 添加新角色

```json
{
  "aliases": {
    "新角色名": [
      "新角色名",
      "简称",
      "昵称",
      "english_name",
      "EnglishName",
      "ENGLISH_NAME"
    ]
  }
}
```

## PowerShell 示例

```powershell
# 登录获取token
$loginData = "username=admin&password=admin123"
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/auth/login" -Method Post -Body $loginData -ContentType "application/x-www-form-urlencoded"
$token = $loginResponse.access_token

# 获取别名配置
$headers = @{ "Authorization" = "Bearer $token" }
$aliases = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/characters/aliases" -Headers $headers

# 显示可用别名
Write-Host "可用的角色别名:"
foreach ($character in $aliases.aliases_by_character.Keys) {
    $aliasesStr = $aliases.aliases_by_character[$character] -join ", "
    Write-Host "  $character`: $aliasesStr"
}

# 使用别名评分
$form = @{
    file = Get-Item "image.png"
    character_name = "卡卡"  # 使用别名
    situation = "4"
}
$result = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/scores/calculate" -Method Post -Form $form -Headers $headers
Write-Host "评分结果: $($result.total_score)"
```

## 错误处理

### 无效别名
```json
{
  "detail": "未找到角色'无效别名'的配置，请先导入角色配置。支持的别名请查看character_aliases.json文件"
}
```

### 配置文件错误
- 如果配置文件格式错误，系统会记录警告日志
- 别名功能会被禁用，但不影响使用原角色名

## 最佳实践

### 1. 别名设计原则
- **简洁性**: 使用简短易记的别名
- **一致性**: 同类角色使用相似的别名规则
- **多样性**: 提供中文、英文、拼音等多种选择
- **唯一性**: 确保每个别名只对应一个角色

### 2. 常用别名类型
- **中文简称**: 卡提希娅 → 卡提
- **昵称**: 卡提希娅 → 卡卡
- **英文名**: 卡提希娅 → katishiya
- **缩写**: 卡提希娅 → KT
- **称呼**: 卡提希娅 → 卡老师

### 3. 维护建议
- **定期更新**: 根据用户反馈添加常用别名
- **版本控制**: 将配置文件纳入版本控制
- **文档同步**: 更新别名时同步更新文档
- **测试验证**: 添加新别名后进行测试

## 技术实现

### 别名解析流程
1. **加载配置**: 从 `character_aliases.json` 加载别名映射
2. **缓存机制**: 首次加载后缓存在内存中
3. **大小写处理**: 别名匹配不区分大小写
4. **回退机制**: 如果别名不存在，使用原输入作为角色名

### 性能优化
- **内存缓存**: 别名映射缓存在内存中，避免重复读取文件
- **快速查找**: 使用哈希表实现O(1)时间复杂度的别名查找
- **延迟加载**: 只在首次使用时加载配置文件

## 故障排除

### 常见问题

1. **别名不生效**
   - 检查配置文件格式是否正确
   - 确认服务已重启
   - 查看日志中的错误信息

2. **配置文件找不到**
   - 确认文件位于项目根目录
   - 检查文件名是否正确
   - 验证文件权限

3. **别名冲突**
   - 确保每个别名只对应一个角色
   - 检查是否有重复的别名定义

现在您可以使用更灵活的别名来进行角色评分了！🎉
