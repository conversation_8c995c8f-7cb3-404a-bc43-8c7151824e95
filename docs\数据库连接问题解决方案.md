# 数据库连接问题解决方案

## 🐛 问题描述

用户在访问API时遇到错误：
```json
{
  "detail": "数据库服务不可用，请检查数据库配置"
}
```

## 🔍 问题分析

### 可能的原因

1. **环境问题**: API服务器没有在正确的conda环境中运行
2. **数据库连接状态**: 启动时数据库连接状态没有正确维护
3. **依赖导入问题**: 全局变量在导入时可能还是初始值

## ✅ 解决方案

### 方案1: 确保在正确环境中启动（推荐）

#### 步骤1: 激活conda环境
```bash
# 在PowerShell中
conda activate 3.9
```

#### 步骤2: 验证环境
```bash
# 检查Python版本
python --version

# 检查关键包
python -c "import fastapi, sqlalchemy, psycopg2; print('✅ 依赖正常')"
```

#### 步骤3: 启动API服务器
```bash
python main.py
```

### 方案2: 使用SQLite数据库（备选方案）

如果PostgreSQL连接有问题，可以切换到SQLite：

#### 修改 `.env` 文件
```bash
# 注释PostgreSQL配置
# DATABASE_URL=postgresql://smartscoring:password123@localhost:5432/smartscoring_db

# 启用SQLite配置
DATABASE_URL=sqlite:///./smartscoring.db
```

#### 重新初始化数据库
```bash
python init_db.py
```

#### 启动服务器
```bash
python main.py
```

### 方案3: 检查PostgreSQL服务

#### Windows系统
```bash
# 检查PostgreSQL服务状态
sc query postgresql-x64-13

# 启动PostgreSQL服务
net start postgresql-x64-13
```

#### 测试数据库连接
```bash
# 使用psql测试连接
psql -h localhost -U smartscoring -d smartscoring_db
```

## 🔧 已修复的技术问题

### 1. 修复了依赖注入问题

**修改文件**: `app/api/deps.py`

**修复前**:
```python
from app.db.session import SessionLocal, database_available

def get_db():
    if not database_available or SessionLocal is None:  # ❌ 使用导入时的值
        raise HTTPException(...)
```

**修复后**:
```python
from app.db.session import get_database_status

def get_db():
    db_status = get_database_status()  # ✅ 获取实时状态
    if not db_status['available']:
        raise HTTPException(...)
    
    from app.db.session import SessionLocal  # ✅ 动态导入
    if SessionLocal is None:
        raise HTTPException(...)
```

### 2. 修复了会话工厂初始化问题

**修改文件**: `app/db/session.py`

**修复前**:
```python
def init_database():
    SessionLocal = sessionmaker(...)  # ❌ 没有global声明
```

**修复后**:
```python
def init_database():
    global SessionLocal
    SessionLocal = sessionmaker(...)  # ✅ 正确的全局变量赋值
```

## 🧪 验证步骤

### 1. 检查数据库状态
```bash
python -c "
from app.db.session import init_database, get_database_status
success = init_database()
status = get_database_status()
print(f'连接成功: {success}')
print(f'数据库状态: {status}')
"
```

### 2. 启动API服务器
```bash
python main.py
```

### 3. 测试API端点
```bash
# 测试健康检查
curl http://localhost:8000/

# 测试登录
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

## 📋 故障排除清单

### 如果仍然遇到问题

1. **检查conda环境**:
   ```bash
   conda info --envs
   conda activate 3.9
   ```

2. **检查依赖安装**:
   ```bash
   pip list | findstr -i "fastapi sqlalchemy psycopg2"
   ```

3. **检查PostgreSQL服务**:
   ```bash
   sc query postgresql-x64-13
   ```

4. **检查数据库连接**:
   ```bash
   psql -h localhost -U smartscoring -d smartscoring_db
   ```

5. **查看详细日志**:
   ```bash
   # 启用调试模式
   set DEBUG=true
   python main.py
   ```

### 常见错误和解决方案

#### 错误1: "No module named 'fastapi'"
```bash
# 解决方案: 激活正确的conda环境
conda activate 3.9
pip install -r requirements.txt
```

#### 错误2: "No module named 'psycopg2'"
```bash
# 解决方案: 安装PostgreSQL驱动
pip install psycopg2-binary
```

#### 错误3: "connection refused"
```bash
# 解决方案: 启动PostgreSQL服务
net start postgresql-x64-13
```

#### 错误4: "database does not exist"
```bash
# 解决方案: 创建数据库
psql -U postgres
CREATE DATABASE smartscoring_db;
CREATE USER smartscoring WITH PASSWORD 'password123';
GRANT ALL PRIVILEGES ON DATABASE smartscoring_db TO smartscoring;
```

## 🎯 推荐解决流程

### 快速解决（推荐）

1. **激活conda环境**:
   ```bash
   conda activate 3.9
   ```

2. **启动API服务器**:
   ```bash
   python main.py
   ```

3. **如果仍有问题，切换到SQLite**:
   ```bash
   # 编辑.env文件，使用SQLite
   DATABASE_URL=sqlite:///./smartscoring.db
   
   # 重新初始化
   python init_db.py
   
   # 启动服务器
   python main.py
   ```

### 完整解决（生产环境）

1. **确保PostgreSQL服务运行**
2. **验证数据库连接**
3. **在正确环境中启动API**
4. **测试所有功能**

## 🎉 预期结果

修复后，您应该能够：

- ✅ 成功启动API服务器
- ✅ 访问 http://localhost:8000/api/v1/docs
- ✅ 使用 `admin` / `admin123` 登录
- ✅ 获取JWT Token
- ✅ 访问所有API端点
- ✅ 使用OCR和评分功能

现在请按照推荐的解决流程操作，应该能够解决数据库连接问题！
