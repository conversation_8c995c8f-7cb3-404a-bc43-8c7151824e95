# 部署指南

本文档介绍如何部署 Smart Scoring API 系统。

## 🎯 系统概述

Smart Scoring API 是一个基于 FastAPI + PaddleOCR 的智能图片评分系统，支持：
- OCR 文字识别和智能推断
- 繁体字自动转换
- 多种图片格式 (JPEG, PNG, MPO)
- 可选的数据库功能
- 灵活的部署方式

## 📋 环境要求

### 基础要求
- **Python**: 3.9+ (支持 3.9, 3.10, 3.11, 3.12)
- **内存**: 最少 2GB，推荐 4GB+
- **存储**: 最少 5GB 可用空间
- **网络**: 稳定的互联网连接（首次运行需下载OCR模型）

### 可选组件
- **PostgreSQL**: 12+ (用户管理、评分历史)
- **SQLite**: 3.x (轻量级数据库选项)
- **Redis**: 6+ (缓存，可选)

## 🚀 快速开始

### 1. 获取项目

```bash
# 克隆项目
git clone <repository-url>
cd smart-scoring-api

# 或下载压缩包并解压
```

### 2. 安装依赖

#### 方式一：使用 Poetry (推荐)
```bash
# 安装 Poetry
pip install poetry

# 安装项目依赖
poetry install

# 激活虚拟环境
poetry shell
```

#### 方式二：使用 pip
```bash
# 创建虚拟环境 (推荐)
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
# Windows: notepad .env
# Linux/Mac: nano .env
```

### 4. 启动服务

```bash
# 直接启动 (无数据库模式)
python main.py

# 或使用 uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000

# 开发模式 (自动重载)
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 5. 访问服务

- **API 服务**: http://localhost:8000
- **API 文档**: http://localhost:8000/api/v1/docs
- **健康检查**: http://localhost:8000/health

## ⚙️ 配置说明

### 环境变量配置

创建 `.env` 文件并配置以下参数：

```bash
# === 基础配置 ===
PROJECT_NAME=Smart Scoring API
VERSION=1.0.0
DEBUG=true
API_V1_STR=/api/v1

# === 数据库配置 (可选) ===
# 未配置时系统将在无数据库模式下运行
# PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name
# SQLite:
# DATABASE_URL=sqlite:///./smartscoring.db
DATABASE_URL=

# === Redis配置 (可选) ===
REDIS_URL=redis://localhost:6379/0

# === 安全配置 ===
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# === 跨域配置 ===
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# === 文件上传配置 ===
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg,image/mpo

# === OCR配置 ===
OCR_USE_GPU=false
OCR_LANG=ch

# === 日志配置 ===
LOG_LEVEL=INFO
```

### 配置项详解

| 配置项 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `DATABASE_URL` | 数据库连接URL | 无 | ❌ |
| `SECRET_KEY` | JWT密钥 | 默认值 | ✅ |
| `DEBUG` | 调试模式 | false | ❌ |
| `CORS_ORIGINS` | 允许的跨域源 | * | ❌ |
| `MAX_FILE_SIZE` | 最大文件大小(字节) | 10MB | ❌ |
| `ALLOWED_FILE_TYPES` | 允许的文件类型 | jpeg,png,jpg,mpo | ❌ |
| `OCR_USE_GPU` | 是否使用GPU加速 | false | ❌ |
| `OCR_LANG` | OCR识别语言 | ch | ❌ |

## 🗄️ 数据库部署

### 无数据库模式 (推荐新手)

系统支持无数据库模式运行，此时：

- ✅ **可用功能**: OCR识别、评分计算、图片处理
- ❌ **不可用功能**: 用户管理、评分历史、权重配置管理

```bash
# 不设置 DATABASE_URL 或设置为空
DATABASE_URL=

# 启动服务
python main.py
```

启动时会看到提示：

```
⚠️ 未配置数据库连接 (DATABASE_URL)，数据库功能将不可用
💡 请在配置文件中设置 DATABASE_URL 以启用数据库功能
⚠️ 应用将在无数据库模式下运行
💡 某些功能（如用户管理、评分历史）将不可用
```

### SQLite 数据库 (推荐开发)

```bash
# 在 .env 文件中设置
DATABASE_URL=sqlite:///./smartscoring.db

# 初始化数据库
python init_db.py

# 启动服务
python main.py
```

### PostgreSQL 数据库 (推荐生产)

#### 1. 安装 PostgreSQL

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib

# macOS
brew install postgresql

# Windows: 下载官方安装包
# https://www.postgresql.org/download/windows/
```

#### 2. 创建数据库

```bash
# 切换到 postgres 用户
sudo -u postgres psql

# 创建数据库和用户
CREATE DATABASE smartscoring_db;
CREATE USER smartscoring WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE smartscoring_db TO smartscoring;
\q
```

#### 3. 配置连接

```bash
# 在 .env 文件中设置
DATABASE_URL=postgresql://smartscoring:your_password@localhost:5432/smartscoring_db

# 初始化数据库
python init_db.py

# 启动服务
python main.py
```

## 🐳 Docker 部署 (已移除)

**注意**: 为了简化部署，我们已经移除了 Docker 配置。推荐使用上述的直接部署方式。

如果您需要容器化部署，可以：

1. 使用系统的 Python 环境直接运行
2. 自行创建 Dockerfile (参考其他 FastAPI 项目)
3. 使用 systemd 或 supervisor 进行进程管理

## 🔧 生产部署

### 1. 系统服务配置

创建 systemd 服务文件 `/etc/systemd/system/smartscoring.service`:

```ini
[Unit]
Description=Smart Scoring API
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/smart-scoring-api
Environment=PATH=/path/to/smart-scoring-api/venv/bin
EnvironmentFile=/path/to/smart-scoring-api/.env
ExecStart=/path/to/smart-scoring-api/venv/bin/python main.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

启动服务:

```bash
# 重载配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable smartscoring

# 启动服务
sudo systemctl start smartscoring

# 查看状态
sudo systemctl status smartscoring

# 查看日志
sudo journalctl -u smartscoring -f
```

### 2. 反向代理 (Nginx)

安装 Nginx:

```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

配置文件 `/etc/nginx/sites-available/smartscoring`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 文件上传大小限制
    client_max_body_size 10M;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 静态文件 (如果有)
    location /static/ {
        alias /path/to/smart-scoring-api/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启用配置:

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/smartscoring /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

### 3. SSL 证书 (Let's Encrypt)

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 4. 安全配置

```bash
# 生成安全密钥
python -c "import secrets; print(secrets.token_urlsafe(32))"

# 更新 .env 文件
SECRET_KEY=your-generated-secret-key
DEBUG=false
CORS_ORIGINS=https://your-domain.com
```

## 📊 监控和维护

### 1. 日志管理

日志文件位置:

```
logs/
├── app.log      # 应用日志
├── access.log   # 访问日志
└── error.log    # 错误日志
```

查看日志:

```bash
# 实时查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 搜索特定错误
grep "ERROR" logs/app.log

# 查看系统服务日志
sudo journalctl -u smartscoring -f
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查数据库状态 (如果配置了数据库)
curl http://localhost:8000/api/v1/health/db

# 检查 OCR 功能
curl -X POST http://localhost:8000/api/v1/scores/calculate-base64 \
  -H "Content-Type: application/json" \
  -d '{"image_data": "data:image/jpeg;base64,..."}'
```

### 3. 性能优化

```bash
# 使用多进程 (Gunicorn)
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# 启用 GPU 加速 (如果有 GPU)
export OCR_USE_GPU=true

# 调整文件上传限制
export MAX_FILE_SIZE=20971520  # 20MB
```

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败

```bash
# 检查数据库服务状态
sudo systemctl status postgresql

# 测试连接
psql -h localhost -U smartscoring -d smartscoring_db

# 检查配置
echo $DATABASE_URL
```

**解决方案**:

- 确保数据库服务正在运行
- 检查用户名、密码、数据库名是否正确
- 检查防火墙设置
- 如果不需要数据库功能，可以不配置 `DATABASE_URL`

#### 2. OCR 识别失败

```bash
# 检查 PaddleOCR 安装
python -c "import paddleocr; print('PaddleOCR OK')"

# 检查模型文件
ls ~/.paddleocr/

# 重新下载模型
rm -rf ~/.paddleocr/
python -c "import paddleocr; paddleocr.PaddleOCR(use_angle_cls=True, lang='ch')"
```

**解决方案**:

- 确保网络连接正常（首次运行需下载模型）
- 检查磁盘空间是否充足
- 尝试重新安装 PaddleOCR: `pip install --upgrade paddleocr`

#### 3. 文件上传失败

```bash
# 检查上传目录
ls -la uploads/

# 检查权限
chmod 755 uploads/

# 检查文件大小限制
grep MAX_FILE_SIZE .env
```

**解决方案**:

- 确保 `uploads/` 目录存在且可写
- 检查文件大小是否超过限制
- 检查文件格式是否支持

#### 4. 端口占用

```bash
# 检查端口占用
netstat -tlnp | grep :8000
# 或
lsof -i :8000

# 杀死占用进程
kill -9 <PID>
```

#### 5. 权限问题

```bash
# 检查文件权限
ls -la

# 修改权限
chmod +x main.py
chown -R $USER:$USER .
```

### 日志分析

```bash
# 查看启动日志
sudo journalctl -u smartscoring --since "1 hour ago"

# 查看错误日志
grep -i error logs/app.log | tail -20

# 查看访问日志
tail -100 logs/access.log

# 实时监控
tail -f logs/app.log | grep -i error
```

## 🔄 更新和备份

### 更新系统

```bash
# 停止服务
sudo systemctl stop smartscoring

# 备份当前版本
cp -r . ../smartscoring-backup-$(date +%Y%m%d)

# 拉取最新代码
git pull origin main

# 更新依赖
poetry install
# 或
pip install -r requirements.txt

# 重启服务
sudo systemctl start smartscoring

# 检查状态
sudo systemctl status smartscoring
```

### 数据备份

```bash
# PostgreSQL 备份
pg_dump -h localhost -U smartscoring smartscoring_db > backup-$(date +%Y%m%d).sql

# SQLite 备份
cp smartscoring.db backup-smartscoring-$(date +%Y%m%d).db

# 配置文件备份
tar -czf config-backup-$(date +%Y%m%d).tar.gz .env character/ logs/
```

### 恢复数据

```bash
# PostgreSQL 恢复
psql -h localhost -U smartscoring smartscoring_db < backup-20231201.sql

# SQLite 恢复
cp backup-smartscoring-20231201.db smartscoring.db
```

## 📞 技术支持

### 获取帮助

如果遇到问题，请提供以下信息：

1. **系统信息**:

   ```bash
   python --version
   pip list | grep -E "(fastapi|paddleocr|sqlalchemy)"
   uname -a  # Linux/Mac
   ```

2. **错误日志**:

   ```bash
   tail -50 logs/error.log
   sudo journalctl -u smartscoring --since "1 hour ago"
   ```

3. **配置信息** (隐藏敏感信息):

   ```bash
   cat .env | grep -v -E "(SECRET_KEY|PASSWORD|TOKEN)"
   ```

### 常用命令速查

```bash
# 启动服务
python main.py

# 查看服务状态
sudo systemctl status smartscoring

# 查看日志
tail -f logs/app.log

# 重启服务
sudo systemctl restart smartscoring

# 检查健康状态
curl http://localhost:8000/health

# 查看 API 文档
# 浏览器访问: http://localhost:8000/api/v1/docs
```

---

## 🎉 部署完成

恭喜！您已经成功部署了 Smart Scoring API 系统。

- **无数据库模式**: 适合快速体验和 OCR 功能测试
- **SQLite 模式**: 适合小规模使用和开发测试
- **PostgreSQL 模式**: 适合生产环境和大规模使用

现在您可以：

1. 访问 API 文档: http://localhost:8000/api/v1/docs
2. 测试 OCR 识别功能
3. 上传图片进行评分计算
4. 享受繁体字自动转换功能

祝您使用愉快！🚀
