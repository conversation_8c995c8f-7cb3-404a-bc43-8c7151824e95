"""
角色配置服务
自动读取character目录下的角色权重配置文件
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from sqlalchemy.orm import Session

from app.crud import weight_config
from app.models.weight_config import WeightConfig
from app.schemas.weight_config import WeightConfigCreate

logger = logging.getLogger(__name__)


class CharacterConfigService:
    """角色配置服务类"""
    
    def __init__(self, character_dir: str = "character", aliases_file: str = "character_aliases.json"):
        """
        初始化角色配置服务

        Args:
            character_dir: 角色配置目录路径
            aliases_file: 角色别名配置文件路径
        """
        self.character_dir = Path(character_dir)
        self.aliases_file = Path(aliases_file)
        self._aliases_cache = None

    def load_aliases(self) -> Dict[str, str]:
        """
        加载角色别名配置

        Returns:
            别名到角色名的映射字典
        """
        if self._aliases_cache is not None:
            return self._aliases_cache

        aliases_map = {}

        try:
            if self.aliases_file.exists():
                with open(self.aliases_file, 'r', encoding='utf-8') as f:
                    aliases_config = json.load(f)

                # 构建别名到角色名的映射
                for character_name, alias_list in aliases_config.get("aliases", {}).items():
                    for alias in alias_list:
                        # 别名不区分大小写
                        aliases_map[alias.lower()] = character_name

                logger.info(f"加载角色别名配置成功: {len(aliases_map)} 个别名")
            else:
                logger.warning(f"角色别名配置文件不存在: {self.aliases_file}")

        except Exception as e:
            logger.error(f"加载角色别名配置失败: {e}")

        self._aliases_cache = aliases_map
        return aliases_map

    def resolve_character_name(self, name_or_alias: str) -> str:
        """
        解析角色名或别名，返回真实的角色名

        Args:
            name_or_alias: 角色名或别名

        Returns:
            真实的角色名
        """
        if not name_or_alias:
            return name_or_alias

        aliases_map = self.load_aliases()

        # 先尝试直接匹配（不区分大小写）
        lower_name = name_or_alias.lower()
        if lower_name in aliases_map:
            resolved_name = aliases_map[lower_name]
            logger.debug(f"别名解析: '{name_or_alias}' -> '{resolved_name}'")
            return resolved_name

        # 如果没有找到别名，返回原名
        return name_or_alias
    
    def scan_character_configs(self) -> List[Dict]:
        """
        扫描所有角色配置文件
        
        Returns:
            角色配置列表
        """
        configs = []
        
        if not self.character_dir.exists():
            print(f"⚠️  角色配置目录不存在: {self.character_dir}")
            return configs
        
        # 遍历所有角色目录
        for character_path in self.character_dir.iterdir():
            if character_path.is_dir():
                config_file = character_path / "calc.json"
                
                if config_file.exists():
                    try:
                        config_data = self.load_character_config(config_file)
                        if config_data:
                            config_data["character_name"] = character_path.name
                            configs.append(config_data)
                            print(f"✅ 加载角色配置: {character_path.name}")
                    except Exception as e:
                        print(f"❌ 加载角色配置失败 {character_path.name}: {e}")
                else:
                    print(f"⚠️  角色配置文件不存在: {config_file}")
        
        print(f"📊 总共扫描到 {len(configs)} 个角色配置")
        return configs
    
    def load_character_config(self, config_file: Path) -> Optional[Dict]:
        """
        加载单个角色配置文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            配置数据字典
        """
        try:
            # 尝试不同的编码方式
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
            config_data = None

            for encoding in encodings:
                try:
                    with open(config_file, 'r', encoding=encoding) as f:
                        config_data = json.load(f)
                    break
                except (UnicodeDecodeError, json.JSONDecodeError):
                    continue

            if config_data is None:
                raise ValueError("无法使用任何编码读取文件")
            
            # 验证配置文件结构
            if not self.validate_config_structure(config_data):
                print(f"❌ 配置文件结构无效: {config_file}")
                return None
            
            return config_data
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误 {config_file}: {e}")
            return None
        except Exception as e:
            print(f"❌ 读取配置文件失败 {config_file}: {e}")
            return None
    
    def validate_config_structure(self, config_data: Dict) -> bool:
        """
        验证配置文件结构
        
        Args:
            config_data: 配置数据
            
        Returns:
            是否有效
        """
        required_fields = ["name", "main_props", "sub_props", "score_max"]
        
        for field in required_fields:
            if field not in config_data:
                print(f"❌ 缺少必需字段: {field}")
                return False
        
        # 验证main_props结构
        if not isinstance(config_data["main_props"], dict):
            print("❌ main_props必须是字典类型")
            return False
        
        # 验证sub_props结构
        if not isinstance(config_data["sub_props"], dict):
            print("❌ sub_props必须是字典类型")
            return False
        
        # 验证score_max结构
        if not isinstance(config_data["score_max"], list):
            print("❌ score_max必须是数组类型")
            return False
        
        return True
    
    def convert_to_api_format(self, config_data: Dict) -> Dict:
        """
        将角色配置转换为API格式
        
        Args:
            config_data: 原始配置数据
            
        Returns:
            API格式的配置数据
        """
        # 创建情境映射 - 固定映射：score_max[0]→COST1, score_max[1]→COST3, score_max[2]→COST4
        main_props = config_data["main_props"]
        situations = list(main_props.keys())

        # 定义固定的索引映射：COST → 索引
        fixed_mapping = {
            "1": 0,  # COST1 → score_max[0]
            "3": 1,  # COST3 → score_max[1]
            "4": 2   # COST4 → score_max[2]
        }

        # 创建situation_map，只包含存在的情境
        situation_map = {}
        for situation in situations:
            if situation in fixed_mapping:
                situation_map[situation] = fixed_mapping[situation]

        # 处理不在固定映射中的其他情境（如果有的话）
        next_index = 3  # 从索引3开始分配给其他情境
        for situation in situations:
            if situation not in situation_map:
                situation_map[situation] = next_index
                next_index += 1
        
        # 直接使用配置文件中的score_max数组，按照固定映射逻辑
        # score_max[0] → COST1, score_max[1] → COST3, score_max[2] → COST4
        score_max = config_data["score_max"].copy()

        # 确保score_max数组长度足够
        while len(score_max) < len(situations):
            # 如果score_max长度不够，用最后一个值填充
            last_score = score_max[-1] if score_max else 75.0
            score_max.append(last_score)
        
        return {
            "name": config_data["name"],
            "description": f"从角色配置文件自动导入: {config_data.get('character_name', 'unknown')}",
            "main_props": main_props,
            "sub_props": config_data["sub_props"],
            "score_max": score_max,
            "situation_map": situation_map
        }
    
    def import_to_database(self, db: Session, force_update: bool = False) -> Tuple[int, int]:
        """
        将角色配置导入数据库
        
        Args:
            db: 数据库会话
            force_update: 是否强制更新已存在的配置
            
        Returns:
            (成功导入数量, 跳过数量)
        """
        configs = self.scan_character_configs()
        imported_count = 0
        skipped_count = 0
        
        for config_data in configs:
            try:
                # 转换为API格式
                api_config = self.convert_to_api_format(config_data)
                
                # 检查是否已存在
                existing_config = weight_config.get_by_name(db, name=api_config["name"])
                
                if existing_config:
                    if force_update:
                        # 更新现有配置
                        updated_config = weight_config.update(
                            db, 
                            db_obj=existing_config, 
                            obj_in=api_config
                        )
                        print(f"🔄 更新配置: {api_config['name']}")
                        imported_count += 1
                    else:
                        print(f"⏭️  跳过已存在的配置: {api_config['name']}")
                        skipped_count += 1
                else:
                    # 创建新配置
                    config_create = WeightConfigCreate(**api_config)
                    new_config = weight_config.create(db, obj_in=config_create)
                    print(f"✅ 导入新配置: {api_config['name']} (ID: {new_config.id})")
                    imported_count += 1
                    
            except Exception as e:
                print(f"❌ 导入配置失败 {config_data.get('name', 'unknown')}: {e}")
                skipped_count += 1
        
        print(f"\n📊 导入统计:")
        print(f"   成功导入: {imported_count}")
        print(f"   跳过配置: {skipped_count}")
        print(f"   总计处理: {imported_count + skipped_count}")
        
        return imported_count, skipped_count
    
    def get_character_list(self) -> List[str]:
        """
        获取所有角色名称列表
        
        Returns:
            角色名称列表
        """
        character_names = []
        
        if not self.character_dir.exists():
            return character_names
        
        for character_path in self.character_dir.iterdir():
            if character_path.is_dir() and (character_path / "calc.json").exists():
                character_names.append(character_path.name)
        
        return sorted(character_names)

    def get_character_list_with_config_ids(self, db: Session) -> List[Dict]:
        """
        获取所有可用角色列表，包含config_id信息

        Args:
            db: 数据库会话

        Returns:
            角色信息列表，包含角色名和对应的config_id
        """
        try:
            # 获取文件系统中的角色列表
            characters = self.get_character_list()

            # 查询数据库中的配置ID
            result = []
            for character_name in characters:
                # 查找数据库中对应的配置
                config = db.query(WeightConfig).filter(
                    WeightConfig.name.like(f"%{character_name}%")
                ).first()

                character_info = {
                    "character_name": character_name,
                    "config_id": config.id if config else None,
                    "config_name": config.name if config else None,
                    "has_config": config is not None
                }
                result.append(character_info)

            logger.info(f"返回 {len(result)} 个角色信息，其中 {sum(1 for r in result if r['has_config'])} 个已导入数据库")
            return result

        except Exception as e:
            logger.error(f"获取角色列表失败: {e}")
            raise

    def get_character_config(self, character_name_or_alias: str) -> Optional[Dict]:
        """
        获取指定角色的配置（支持别名）

        Args:
            character_name_or_alias: 角色名称或别名

        Returns:
            角色配置数据
        """
        # 解析别名到真实角色名
        character_name = self.resolve_character_name(character_name_or_alias)

        character_path = self.character_dir / character_name
        config_file = character_path / "calc.json"

        if not config_file.exists():
            return None

        return self.load_character_config(config_file)

    def find_config_by_name_or_alias(self, db: Session, name_or_alias: str) -> Optional[WeightConfig]:
        """
        根据角色名或别名查找数据库中的配置

        Args:
            db: 数据库会话
            name_or_alias: 角色名或别名

        Returns:
            权重配置对象
        """
        # 解析别名到真实角色名
        character_name = self.resolve_character_name(name_or_alias)

        # 在数据库中查找配置
        config = db.query(WeightConfig).filter(
            WeightConfig.name.like(f"%{character_name}%")
        ).first()

        if config:
            logger.debug(f"找到配置: '{name_or_alias}' -> '{character_name}' -> Config ID {config.id}")
        else:
            logger.debug(f"未找到配置: '{name_or_alias}' -> '{character_name}'")

        return config


# 创建全局角色配置服务实例
character_service = CharacterConfigService()
