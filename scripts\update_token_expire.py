#!/usr/bin/env python3
"""
Token有效期快速设置脚本
用于快速修改JWT token的有效期设置
"""

import os
import re
import sys
from pathlib import Path


def update_env_file(env_file_path: str, expire_minutes: int) -> bool:
    """
    更新.env文件中的ACCESS_TOKEN_EXPIRE_MINUTES设置
    
    Args:
        env_file_path: .env文件路径
        expire_minutes: 新的有效期（分钟）
        
    Returns:
        是否更新成功
    """
    try:
        env_path = Path(env_file_path)
        
        if not env_path.exists():
            print(f"❌ 环境文件不存在: {env_file_path}")
            return False
        
        # 读取现有内容
        with open(env_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否存在ACCESS_TOKEN_EXPIRE_MINUTES配置
        pattern = r'^ACCESS_TOKEN_EXPIRE_MINUTES=\d+$'
        
        if re.search(pattern, content, re.MULTILINE):
            # 更新现有配置
            new_content = re.sub(
                pattern, 
                f'ACCESS_TOKEN_EXPIRE_MINUTES={expire_minutes}', 
                content, 
                flags=re.MULTILINE
            )
            print(f"✅ 更新现有配置: ACCESS_TOKEN_EXPIRE_MINUTES={expire_minutes}")
        else:
            # 添加新配置
            if not content.endswith('\n'):
                content += '\n'
            new_content = content + f'ACCESS_TOKEN_EXPIRE_MINUTES={expire_minutes}\n'
            print(f"✅ 添加新配置: ACCESS_TOKEN_EXPIRE_MINUTES={expire_minutes}")
        
        # 写入更新后的内容
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False


def get_current_setting(env_file_path: str) -> int:
    """
    获取当前的token有效期设置
    
    Args:
        env_file_path: .env文件路径
        
    Returns:
        当前有效期（分钟），如果未找到返回-1
    """
    try:
        env_path = Path(env_file_path)
        
        if not env_path.exists():
            return -1
        
        with open(env_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        match = re.search(r'^ACCESS_TOKEN_EXPIRE_MINUTES=(\d+)$', content, re.MULTILINE)
        if match:
            return int(match.group(1))
        
        return -1
        
    except Exception:
        return -1


def format_duration(minutes: int) -> str:
    """
    格式化时间显示
    
    Args:
        minutes: 分钟数
        
    Returns:
        格式化的时间字符串
    """
    if minutes < 60:
        return f"{minutes}分钟"
    elif minutes < 1440:
        hours = minutes // 60
        remaining_minutes = minutes % 60
        if remaining_minutes == 0:
            return f"{hours}小时"
        else:
            return f"{hours}小时{remaining_minutes}分钟"
    else:
        days = minutes // 1440
        remaining_hours = (minutes % 1440) // 60
        if remaining_hours == 0:
            return f"{days}天"
        else:
            return f"{days}天{remaining_hours}小时"


def get_preset_options():
    """获取预设选项"""
    return {
        "1": (15, "15分钟 - 高安全性（适合敏感操作）"),
        "2": (30, "30分钟 - 标准设置（推荐生产环境）"),
        "3": (60, "1小时 - 用户友好（适合一般使用）"),
        "4": (120, "2小时 - 开发测试"),
        "5": (480, "8小时 - 长期开发"),
        "6": (1440, "24小时 - 超长期（不推荐生产环境）"),
        "0": (0, "自定义时间")
    }


def main():
    """主函数"""
    print("🔧 JWT Token有效期设置工具")
    print("=" * 50)
    
    # 确定.env文件路径
    env_file = ".env"
    if len(sys.argv) > 1:
        env_file = sys.argv[1]
    
    # 显示当前设置
    current_minutes = get_current_setting(env_file)
    if current_minutes > 0:
        print(f"📋 当前设置: {format_duration(current_minutes)} ({current_minutes}分钟)")
    else:
        print("📋 当前设置: 未配置或使用默认值(30分钟)")
    
    print(f"📁 配置文件: {os.path.abspath(env_file)}")
    print()
    
    # 显示预设选项
    print("🎯 请选择新的有效期:")
    options = get_preset_options()
    
    for key, (minutes, description) in options.items():
        if key == "0":
            print(f"  {key}. {description}")
        else:
            print(f"  {key}. {description}")
    
    print()
    
    # 获取用户选择
    while True:
        choice = input("请输入选项编号 (1-6, 0为自定义, q退出): ").strip()
        
        if choice.lower() == 'q':
            print("👋 已取消操作")
            return
        
        if choice in options:
            minutes, description = options[choice]
            
            if choice == "0":
                # 自定义时间
                while True:
                    try:
                        custom_minutes = input("请输入自定义有效期（分钟，5-10080）: ").strip()
                        minutes = int(custom_minutes)
                        
                        if 5 <= minutes <= 10080:  # 5分钟到7天
                            break
                        else:
                            print("❌ 有效期必须在5分钟到7天(10080分钟)之间")
                    except ValueError:
                        print("❌ 请输入有效的数字")
            
            break
        else:
            print("❌ 无效选择，请重新输入")
    
    # 确认修改
    print()
    print(f"🔄 准备将Token有效期设置为: {format_duration(minutes)} ({minutes}分钟)")
    
    confirm = input("确认修改? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("👋 已取消操作")
        return
    
    # 执行修改
    print()
    print("🔧 正在更新配置...")
    
    if update_env_file(env_file, minutes):
        print(f"✅ 配置更新成功!")
        print(f"📝 新设置: ACCESS_TOKEN_EXPIRE_MINUTES={minutes}")
        print(f"⏰ 有效期: {format_duration(minutes)}")
        print()
        print("⚠️  重要提醒:")
        print("   1. 需要重启服务才能使新配置生效")
        print("   2. 已发出的token仍按原有效期执行")
        print("   3. 新配置只影响新生成的token")
        print()
        print("🚀 重启服务命令:")
        print("   Docker: docker-compose restart api")
        print("   直接运行: 停止服务后重新启动")
    else:
        print("❌ 配置更新失败")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 操作已取消")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)
