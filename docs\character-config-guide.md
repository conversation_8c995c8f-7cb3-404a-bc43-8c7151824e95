# 角色配置功能使用指南

## 功能概述

Smart Scoring API 支持自动读取 `character` 目录下的角色配置文件，实现了：

- 🎮 **自动发现**: 扫描 `character/*/calc.json` 文件
- 📥 **批量导入**: 一键导入所有角色配置到数据库
- 🔄 **格式转换**: 自动转换为API标准格式
- 🌐 **多编码支持**: 支持UTF-8、GBK等多种编码格式
- ✅ **数据验证**: 完整的配置文件结构验证

## 目录结构

```
character/
├── default/
│   └── calc.json
├── 今汐/
│   └── calc.json
├── 安可/
│   └── calc.json
├── 长离/
│   └── calc.json
└── ... (更多角色)
```

## 配置文件格式

每个角色的 `calc.json` 文件应包含以下结构：

```json
{
  "name": "角色名-配置名",
  "main_props": {
    "c4": {
      "攻击力": 0.5,
      "暴击率": 0.8,
      "暴击伤害": 0.9
    },
    "c1": {
      "攻击力": 0.4,
      "暴击率": 0.7
    }
  },
  "sub_props": {
    "攻击力": 1.0,
    "暴击率": 1.2,
    "暴击伤害": 1.1,
    "元素精通": 0.8,
    "充能效率": 0.6
  },
  "score_max": [75.0, 80.0]
}
```

### 字段说明

- **name**: 配置名称，用于在数据库中标识
- **main_props**: 主词条权重配置，按情境分组
  - 键为情境标识符（如 "c4", "c1"）
  - 值为该情境下的词条权重映射
- **sub_props**: 副词条权重配置，所有情境通用
- **score_max**: 各情境的未对齐最高分数组

## API 接口

### 1. 获取角色列表

**端点**: `GET /api/v1/characters/list`

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/characters/list"
```

**响应示例**:
```json
[
  "default",
  "今汐", 
  "安可",
  "长离",
  "..."
]
```

### 2. 获取角色配置

**端点**: `GET /api/v1/characters/{character_name}/config`

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/characters/今汐/config"
```

**响应示例**:
```json
{
  "name": "今汐-通用配置",
  "character_name": "今汐",
  "main_props": {
    "c4": {
      "攻击力": 0.5,
      "暴击率": 0.8
    }
  },
  "sub_props": {
    "攻击力": 1.0,
    "暴击率": 1.2
  },
  "score_max": [75.0]
}
```

### 3. 获取角色支持的情境

**端点**: `GET /api/v1/characters/{character_name}/situations`

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/characters/今汐/situations"
```

**响应示例**:
```json
{
  "character_name": "今汐",
  "situations": ["c4", "c1"],
  "situation_count": 2
}
```

### 4. 扫描角色配置（管理员）

**端点**: `GET /api/v1/characters/scan`

```bash
curl -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/characters/scan"
```

**响应示例**:
```json
{
  "message": "角色配置扫描完成",
  "total_configs": 39,
  "characters": [
    {
      "character_name": "今汐",
      "config_name": "今汐-通用配置",
      "situations": ["c4", "c1"],
      "situation_count": 2
    }
  ]
}
```

### 5. 导入所有角色配置（管理员）

**端点**: `POST /api/v1/characters/import`

```bash
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/characters/import?force_update=false"
```

**响应示例**:
```json
{
  "message": "角色配置导入完成",
  "imported_count": 35,
  "skipped_count": 4,
  "total_processed": 39,
  "force_update": false
}
```

### 6. 导入单个角色配置（管理员）

**端点**: `POST /api/v1/characters/{character_name}/import`

```bash
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/characters/今汐/import?force_update=true"
```

**响应示例**:
```json
{
  "message": "角色配置导入成功: 今汐",
  "config_id": 15,
  "config_name": "今汐-通用配置",
  "action": "created"
}
```

## 自动导入功能

### 数据库初始化时自动导入

运行 `python init_db.py` 时，系统会自动：

1. 扫描 `character` 目录下的所有角色配置
2. 验证配置文件格式
3. 转换为API标准格式
4. 导入到数据库中

```bash
python init_db.py
```

输出示例：
```
✅ 加载角色配置: 今汐
✅ 加载角色配置: 安可
✅ 加载角色配置: 长离
...
📊 总共扫描到 39 个角色配置
✅ 导入新配置: 今汐-通用配置 (ID: 15)
✅ 导入新配置: 安可-通用配置 (ID: 16)
...
📊 导入统计:
   成功导入: 35
   跳过配置: 4
   总计处理: 39
```

### 格式转换说明

系统会自动将角色配置转换为API标准格式：

**原始格式** → **API格式**

1. **情境映射生成**: 根据 `main_props` 的键自动生成 `situation_map`
2. **分数数组调整**: 确保 `score_max` 长度与情境数量匹配
3. **描述生成**: 自动生成包含角色名的描述信息

示例转换：
```json
// 原始配置
{
  "name": "今汐-通用配置",
  "main_props": {
    "c4": {"攻击力": 0.5},
    "c1": {"攻击力": 0.4}
  },
  "score_max": [75.0]
}

// 转换后的API格式
{
  "name": "今汐-通用配置",
  "description": "从角色配置文件自动导入: 今汐",
  "main_props": {
    "c4": {"攻击力": 0.5},
    "c1": {"攻击力": 0.4}
  },
  "situation_map": {
    "c4": 0,
    "c1": 1
  },
  "score_max": [75.0, 75.0]  // 自动扩展
}
```

## 使用流程

### 1. 添加新角色配置

1. 在 `character` 目录下创建角色文件夹
2. 添加 `calc.json` 配置文件
3. 运行导入命令或重启服务

```bash
# 创建新角色目录
mkdir character/新角色

# 创建配置文件
cat > character/新角色/calc.json << 'EOF'
{
  "name": "新角色-通用配置",
  "main_props": {
    "c4": {"攻击力": 0.5, "暴击率": 0.8}
  },
  "sub_props": {
    "攻击力": 1.0,
    "暴击率": 1.2
  },
  "score_max": [75.0]
}
EOF

# 导入到数据库
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/characters/新角色/import"
```

### 2. 更新现有配置

1. 修改对应的 `calc.json` 文件
2. 使用 `force_update=true` 参数重新导入

```bash
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/characters/今汐/import?force_update=true"
```

### 3. 批量管理

```bash
# 扫描所有配置（不导入）
curl -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/characters/scan"

# 批量导入所有配置
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/characters/import"

# 强制更新所有配置
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  "http://localhost:8000/api/v1/characters/import?force_update=true"
```

## 错误处理

### 常见错误

1. **配置文件格式错误**
```json
{
  "error": "ValidationError",
  "message": "配置文件结构无效",
  "details": "缺少必需字段: main_props"
}
```

2. **角色不存在**
```json
{
  "error": "NotFound",
  "message": "角色配置不存在: 不存在的角色"
}
```

3. **编码问题**
```json
{
  "error": "EncodingError", 
  "message": "无法使用任何编码读取文件"
}
```

### 故障排除

1. **检查文件编码**: 确保配置文件使用UTF-8编码
2. **验证JSON格式**: 使用JSON验证工具检查语法
3. **检查文件权限**: 确保应用有读取权限
4. **查看日志**: 检查详细的错误信息

## 最佳实践

### 1. 配置文件管理

- 使用UTF-8编码保存配置文件
- 保持JSON格式的正确性
- 为每个角色使用描述性的配置名称
- 定期备份配置文件

### 2. 权重设计

- 主词条权重范围建议：0.1-2.0
- 副词条权重范围建议：0.1-1.5
- 根据角色特点调整权重比例
- 保持同类角色权重的一致性

### 3. 情境管理

- 使用简洁的情境标识符（如c4, c1）
- 确保情境与score_max数组对应
- 为不同玩法场景设计不同情境

### 4. 版本控制

- 将character目录纳入版本控制
- 记录配置变更的原因和影响
- 在更新前备份现有配置
- 测试配置变更的影响

## 技术实现

角色配置功能的技术实现包括：

- **文件扫描**: 递归扫描character目录
- **编码检测**: 自动尝试多种编码格式
- **格式验证**: 严格的JSON结构验证
- **数据转换**: 自动生成API标准格式
- **批量处理**: 支持批量导入和更新
- **错误处理**: 完善的异常处理机制

这个功能大大简化了权重配置的管理，让用户可以专注于配置内容而不是技术细节。
