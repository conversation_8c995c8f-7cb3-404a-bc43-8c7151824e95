"""
API依赖项
定义API端点的依赖注入，如获取当前用户、数据库会话等
"""

from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.security import verify_token
from app.crud import user
from app.db.session import get_database_status
from app.models.user import User

# HTTP Bearer认证方案
security = HTTPBearer()


def get_db() -> Generator:
    """
    获取数据库会话

    Yields:
        数据库会话对象

    Raises:
        HTTPException: 数据库不可用时抛出503错误
    """
    # 获取实时数据库状态
    db_status = get_database_status()
    if not db_status['available']:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="数据库服务不可用，请检查数据库配置"
        )

    # 动态导入SessionLocal以获取最新状态
    from app.db.session import SessionLocal
    if SessionLocal is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="数据库会话工厂未初始化"
        )

    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()


def get_current_user(
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    获取当前认证用户
    
    Args:
        db: 数据库会话
        credentials: HTTP认证凭据
        
    Returns:
        当前用户对象
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    # 验证JWT令牌
    username = verify_token(credentials.credentials)
    if username is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 获取用户信息
    current_user = user.get_by_username(db, username=username)
    if current_user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户是否激活
    if not user.is_active(current_user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    return current_user


def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前激活用户（已包含在get_current_user中的检查）
    
    Args:
        current_user: 当前用户
        
    Returns:
        当前激活用户
    """
    return current_user


def get_current_admin_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前管理员用户
    
    Args:
        current_user: 当前用户
        
    Returns:
        当前管理员用户
        
    Raises:
        HTTPException: 非管理员用户时抛出403错误
    """
    if not user.is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限"
        )
    return current_user


def get_optional_current_user(
    db: Session = Depends(get_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[User]:
    """
    获取可选的当前用户（用于可选认证的端点）
    
    Args:
        db: 数据库会话
        credentials: HTTP认证凭据（可选）
        
    Returns:
        当前用户对象或None
    """
    if credentials is None:
        return None
    
    try:
        username = verify_token(credentials.credentials)
        if username is None:
            return None
        
        current_user = user.get_by_username(db, username=username)
        if current_user is None or not user.is_active(current_user):
            return None
        
        return current_user
    except Exception:
        return None
