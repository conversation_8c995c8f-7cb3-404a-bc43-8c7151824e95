# Smart Scoring API 快速启动指南

## 🚀 启动方式

### 方式一：直接运行 (推荐)

```bash
# 进入项目目录
cd smart-scoring-api

# 直接运行主程序
python main.py
```

### 方式二：使用 uvicorn

```bash
# 标准启动
python -m uvicorn main:app --host 127.0.0.1 --port 8000

# 开发模式 (热重载)
python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload
```

### 方式三：使用批处理文件 (Windows)

```bash
# 双击运行或命令行执行
start.bat
```

### 方式四：使用 Docker

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f api

# 停止服务
docker-compose down
```

## 🔧 环境准备

### 1. 检查 Python 版本
```bash
python --version
# 需要 Python 3.9 或更高版本
```

### 2. 安装依赖
```bash
# 使用 pip
pip install -r requirements.txt

# 或使用 poetry
poetry install
```

### 3. 配置环境变量
```bash
# 复制环境变量模板
copy .env.example .env

# 编辑 .env 文件 (可选)
notepad .env
```

## 📊 验证服务

### 1. 检查服务状态
访问: http://localhost:8000/health

预期响应:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-09T12:00:00Z",
  "version": "1.0.0"
}
```

### 2. 查看API文档
- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc

### 3. 测试登录
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123"
```

## 🔑 默认账户

| 用户类型 | 用户名 | 密码 |
|---------|--------|------|
| 管理员 | admin | admin123 |
| 普通用户 | testuser | test123 |

## 🛠️ 常见问题

### 问题1: 编码错误
```
UnicodeDecodeError: 'gbk' codec can't decode byte
```

**解决方案**:
```bash
# 设置环境变量
set PYTHONIOENCODING=utf-8

# 然后启动服务
python main.py
```

### 问题2: 端口被占用
```
OSError: [Errno 48] Address already in use
```

**解决方案**:
```bash
# 查找占用端口的进程
netstat -ano | findstr :8000

# 杀死进程 (替换 PID)
taskkill /PID <PID> /F

# 或使用其他端口
python -m uvicorn main:app --host 127.0.0.1 --port 8001
```

### 问题3: 模块导入错误
```
ModuleNotFoundError: No module named 'xxx'
```

**解决方案**:
```bash
# 安装缺失的依赖
pip install -r requirements.txt

# 或重新安装所有依赖
pip install --force-reinstall -r requirements.txt
```

### 问题4: 数据库错误
```
sqlite3.OperationalError: no such table
```

**解决方案**:
```bash
# 初始化数据库
python init_db.py

# 或删除数据库文件重新创建
del smartscoring.db
python init_db.py
```

## 📱 使用示例

### 1. 获取角色列表
```bash
# 登录获取token
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123"

# 使用token获取角色列表
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/characters/list"
```

### 2. 评分计算
```bash
# 使用角色名字
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.png" \
  -F "character_name=卡提希娅" \
  -F "situation=4"

# 使用别名
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.png" \
  -F "character_name=卡卡" \
  -F "situation=4"
```

### 3. 查看别名配置
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/characters/aliases"
```

## 🎯 PowerShell 示例

```powershell
# 启动服务
python main.py

# 等待服务启动后，在新窗口中测试
$loginData = "username=admin&password=admin123"
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/auth/login" -Method Post -Body $loginData -ContentType "application/x-www-form-urlencoded"
$token = $loginResponse.access_token

# 获取角色列表
$headers = @{ "Authorization" = "Bearer $token" }
$characters = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/characters/list" -Headers $headers

# 显示角色信息
$characters | ForEach-Object {
    Write-Host "$($_.character_name): Config ID = $($_.config_id)"
}

# 评分计算 (需要准备图片文件)
$form = @{
    file = Get-Item "image.png"
    character_name = "卡卡"
    situation = "4"
}
$result = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/scores/calculate" -Method Post -Form $form -Headers $headers
Write-Host "评分结果: $($result.total_score)"
```

## 📚 更多信息

- **完整文档**: 查看 `docs/` 目录
- **API文档**: http://localhost:8000/api/v1/docs
- **项目结构**: `docs/project-structure.md`
- **部署指南**: `docs/deployment.md`
- **角色配置**: `docs/character-config-guide.md`
- **别名使用**: `docs/character-aliases-guide.md`

## 🎉 成功启动标志

当您看到以下输出时，表示服务启动成功：

```
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
```

现在您可以访问 http://localhost:8000/api/v1/docs 开始使用API了！
