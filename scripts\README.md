# Token管理脚本

这个目录包含了用于管理JWT token有效期的实用脚本。

## 脚本列表

### 1. `update_token_expire.py` - Token有效期设置工具
快速修改JWT token的有效期配置。

#### 功能特性
- ✅ 显示当前token有效期设置
- ✅ 提供常用预设选项（15分钟到24小时）
- ✅ 支持自定义有效期设置
- ✅ 自动更新.env文件
- ✅ 友好的交互界面

#### 使用方法
```bash
# 使用默认.env文件
python scripts/update_token_expire.py

# 指定特定的环境文件
python scripts/update_token_expire.py /path/to/.env
```

#### 预设选项
- **15分钟** - 高安全性（适合敏感操作）
- **30分钟** - 标准设置（推荐生产环境）
- **1小时** - 用户友好（适合一般使用）
- **2小时** - 开发测试
- **8小时** - 长期开发
- **24小时** - 超长期（不推荐生产环境）
- **自定义** - 5分钟到7天任意设置

### 2. `test_token_expire.py` - Token有效期测试工具
测试JWT token的有效期和过期行为。

#### 功能特性
- ✅ 自动登录获取token
- ✅ 解码显示token信息
- ✅ 快速测试token状态
- ✅ 连续监控直到过期
- ✅ 自定义检查间隔

#### 使用方法
```bash
# 使用默认设置测试
python scripts/test_token_expire.py

# 确保API服务正在运行在 http://localhost:8000
```

#### 测试模式
1. **快速测试** - 立即测试token状态和剩余时间
2. **连续测试** - 每分钟检查一次，直到token过期
3. **自定义间隔** - 自定义检查间隔（5秒-1小时）

## 使用场景

### 场景1：修改token有效期
```bash
# 1. 运行设置工具
python scripts/update_token_expire.py

# 2. 选择合适的有效期（比如选择3 - 1小时）

# 3. 重启服务使配置生效
docker-compose restart api
# 或者直接运行的话，停止后重新启动
```

### 场景2：验证token有效期设置
```bash
# 1. 确保API服务正在运行
# 2. 运行测试工具
python scripts/test_token_expire.py

# 3. 选择快速测试查看当前token信息
# 4. 或选择连续测试监控token过期过程
```

### 场景3：开发环境快速设置
```bash
# 设置为8小时（开发友好）
python scripts/update_token_expire.py
# 选择 5 (8小时)

# 重启服务
docker-compose restart api

# 验证设置
python scripts/test_token_expire.py
# 选择 1 (快速测试)
```

## 注意事项

### 重要提醒
1. **重启服务**：修改有效期后必须重启服务才能生效
2. **现有token**：已发出的token仍按原有效期执行
3. **新token**：新配置只影响新生成的token

### 安全建议
- **生产环境**：建议使用30-60分钟
- **开发环境**：可以使用2-8小时提高开发效率
- **敏感操作**：考虑使用15分钟或更短

### 故障排除

#### 问题1：脚本运行失败
```bash
# 检查Python环境
python --version

# 安装依赖（如果需要）
pip install requests
```

#### 问题2：API连接失败
```bash
# 检查服务是否运行
curl http://localhost:8000/api/v1/auth/login-json

# 检查端口是否正确
netstat -an | grep 8000
```

#### 问题3：.env文件权限问题
```bash
# 检查文件权限
ls -la .env

# 修改权限（如果需要）
chmod 644 .env
```

## 高级用法

### 批量环境配置
```bash
# 为多个环境设置不同的有效期
python scripts/update_token_expire.py .env.dev    # 开发环境
python scripts/update_token_expire.py .env.test   # 测试环境  
python scripts/update_token_expire.py .env.prod   # 生产环境
```

### 自动化脚本
```bash
#!/bin/bash
# auto_setup_dev.sh - 开发环境自动设置

echo "设置开发环境token有效期为8小时..."
echo "5" | python scripts/update_token_expire.py

echo "重启服务..."
docker-compose restart api

echo "等待服务启动..."
sleep 10

echo "验证设置..."
echo "1" | python scripts/test_token_expire.py
```

## 扩展功能

这些脚本可以作为基础，扩展更多功能：

1. **监控告警**：token即将过期时发送通知
2. **批量测试**：测试多个用户的token状态
3. **性能分析**：分析不同有效期对系统性能的影响
4. **自动续期**：实现token自动刷新机制

## 贡献

如果你有改进建议或发现问题，欢迎：
1. 提交Issue报告问题
2. 提交Pull Request改进代码
3. 分享使用经验和最佳实践
