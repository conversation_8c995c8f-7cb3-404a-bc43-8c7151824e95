# 副词条多数值支持说明

## 功能概述

系统现在支持副词条的多个强化等级数值验证。副词条可以通过强化获得不同的数值，系统会检查所有可能的数值，只有当所有数值都不匹配时，才判断为"词条数值未满"。

## 副词条数值表

根据词条数值.md文档，副词条支持以下强化等级：

### 百分比类副词条（8个等级）

| 词条名称 | 8级(满) | 7级 | 6级 | 5级 | 4级 | 3级 | 2级 | 1级 |
|---------|---------|-----|-----|-----|-----|-----|-----|-----|
| 攻击% | 11.60% | 10.90% | 10.10% | 9.40% | 8.60% | 7.90% | 7.10% | 6.46% |
| 生命% | 11.60% | 10.90% | 10.10% | 9.40% | 8.60% | 7.90% | 7.10% | 6.40% |
| 防御% | 15.00% | 13.80% | 12.80% | 11.80% | 10.90% | 10.00% | 9.00% | 8.10% |
| 暴击 | 10.50% | 9.90% | 9.30% | 8.70% | 8.10% | 7.50% | 6.90% | 6.30% |
| 暴击伤害 | 21.00% | 19.80% | 18.60% | 17.40% | 16.20% | 15.00% | 13.80% | 12.60% |
| 共鸣解放伤害加成 | 11.60% | 10.90% | 10.10% | 9.40% | 8.60% | 7.90% | 7.10% | 6.40% |
| 共鸣技能伤害加成 | 11.60% | 10.90% | 10.10% | 9.40% | 8.60% | 7.90% | 7.10% | 6.40% |
| 普攻伤害加成 | 11.60% | 10.90% | 10.10% | 9.40% | 8.60% | 7.90% | 7.10% | 6.40% |
| 重击伤害加成 | 11.60% | 10.90% | 10.10% | 9.40% | 8.60% | 7.90% | 7.10% | 6.40% |
| 共鸣效率 | 12.40% | 11.60% | 10.80% | 10.00% | 9.20% | 8.40% | 7.60% | 6.80% |

### 固定值类副词条

#### 生命（8个等级）
| 8级(满) | 7级 | 6级 | 5级 | 4级 | 3级 | 2级 | 1级 |
|---------|-----|-----|-----|-----|-----|-----|-----|
| 580 | 540 | 510 | 470 | 430 | 390 | 360 | 320 |

#### 攻击（3个等级）
| 3级(满) | 2级 | 1级 |
|---------|-----|-----|
| 50 | 40 | 30 |

#### 防御（3个等级）
| 3级(满) | 2级 | 1级 |
|---------|-----|-----|
| 60 | 50 | 40 |

## 验证逻辑

### 1. 多数值匹配
```python
# 检查是否匹配任何一个可能的数值
for expected_value in possible_values:
    if abs(value - expected_value) <= expected_value * tolerance:
        matched_value = expected_value
        break
```

### 2. 容错机制
- 允许5%的误差范围，考虑OCR识别的精度问题
- 只有当所有可能数值都不匹配时，才判断为"词条数值未满"

### 3. 返回信息
验证成功时返回：
```json
{
  "is_valid": true,
  "term_type": "sub_prop",
  "expected_value": [11.6, 10.9, 10.1, 9.4, 8.6, 7.9, 7.1, 6.46],
  "matched_value": 8.6,
  "error_type": null
}
```

验证失败时返回：
```json
{
  "is_valid": false,
  "term_type": "sub_prop",
  "expected_value": [11.6, 10.9, 10.1, 9.4, 8.6, 7.9, 7.1, 6.46],
  "matched_value": null,
  "error_type": "not_max_level"
}
```

## 实际应用示例

### 示例1：不同强化等级的攻击%
```
OCR识别: "攻击 8.6%"
验证结果: ✅ 有效（匹配4级强化的攻击%）
评分: 正常计算，使用副词条权重
```

### 示例2：不同强化等级的共鸣解放伤害加成
```
OCR识别: "共鸣解放伤害加成 10.1%"
验证结果: ✅ 有效（匹配6级强化）
评分: 正常计算，使用副词条权重
```

### 示例3：无效数值
```
OCR识别: "攻击 5.0%"
验证结果: ❌ 无效（不匹配任何强化等级）
错误信息: "攻击%: 词条数值未满"
```

## 优势特点

### 1. 灵活性
- 支持所有强化等级的副词条
- 不要求副词条必须满级才能评分
- 更贴近游戏实际情况

### 2. 准确性
- 基于官方数值表的精确匹配
- 考虑OCR识别误差的容错机制
- 区分不同词条的不同强化等级数量

### 3. 用户友好
- 只有真正无效的数值才报错
- 提供详细的匹配信息
- 清晰的错误提示

## 与主词条的区别

### 主词条
- 只有一个固定的满级数值
- 必须完全匹配才有效
- 不同情况下数值不同

### 副词条
- 支持多个强化等级数值
- 匹配任意等级都有效
- 所有情况下数值相同

## 技术实现

### 数据结构
```python
"sub_props": {
    "攻击%": [11.6, 10.9, 10.1, 9.4, 8.6, 7.9, 7.1, 6.46],
    "攻击": [50, 40, 30],
    "生命": [580, 540, 510, 470, 430, 390, 360, 320],
    # ... 其他副词条
}
```

### 验证流程
1. 获取词条的所有可能数值
2. 逐一检查是否匹配（考虑误差）
3. 找到匹配值则返回成功
4. 所有值都不匹配则返回失败

## 总结

副词条多数值支持功能让评分系统更加实用和准确：

- ✅ **更贴近实际**：支持不同强化等级的副词条
- ✅ **更加灵活**：不要求副词条必须满级
- ✅ **更高准确性**：基于官方数值表的精确验证
- ✅ **更好体验**：减少误报，提供详细信息

这个改进让系统能够正确处理各种强化等级的副词条，大大提升了评分的实用性和准确性。
