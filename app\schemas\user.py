"""
用户相关的数据模式
定义用户创建、更新、响应等数据结构
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    """用户基础模式"""
    
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    role: str = Field(default="user", description="用户角色", pattern="^(admin|user)$")
    is_active: bool = Field(default=True, description="是否激活")


class UserCreate(UserBase):
    """用户创建模式"""
    
    password: str = Field(..., min_length=6, max_length=100, description="密码")
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "newuser",
                "email": "<EMAIL>",
                "password": "secure_password",
                "role": "user",
                "is_active": True
            }
        }


class UserUpdate(BaseModel):
    """用户更新模式"""
    
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    password: Optional[str] = Field(None, min_length=6, max_length=100, description="新密码")
    role: Optional[str] = Field(None, description="用户角色", pattern="^(admin|user)$")
    is_active: Optional[bool] = Field(None, description="是否激活")


class UserInDB(UserBase):
    """数据库中的用户模式"""
    
    id: int = Field(..., description="用户ID")
    hashed_password: str = Field(..., description="哈希密码")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class UserResponse(UserBase):
    """用户响应模式（不包含敏感信息）"""
    
    id: int = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "username": "admin",
                "email": "<EMAIL>",
                "role": "admin",
                "is_active": True,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        }


class UserListResponse(BaseModel):
    """用户列表响应模式"""
    
    users: list[UserResponse] = Field(..., description="用户列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
