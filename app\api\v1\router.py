"""
API v1路由聚合
将所有v1版本的API端点聚合到一个路由器中
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, configs, scores, characters, users, aliases, logs

# 创建API v1路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(
    auth.router, 
    prefix="/auth", 
    tags=["认证"]
)

api_router.include_router(
    configs.router,
    prefix="/configs",
    tags=["权重配置管理"]
)

api_router.include_router(
    users.router,
    prefix="/users",
    tags=["用户管理"]
)

api_router.include_router(
    scores.router,
    prefix="/scores",
    tags=["评分计算"]
)

api_router.include_router(
    characters.router,
    prefix="/characters",
    tags=["角色配置管理"]
)

api_router.include_router(
    aliases.router,
    prefix="/aliases",
    tags=["别名管理"]
)

api_router.include_router(
    logs.router,
    prefix="/logs",
    tags=["日志管理"]
)
