"""
评分计算API端点
处理图片上传和评分计算功能
"""

import base64
import binascii
import re
from typing import Any, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.schemas.score import ScoreCalculationResponse, ErrorResponse, Base64ImageRequest
from app.services import score_service
from app.services.character_service import character_service

router = APIRouter()


def _decode_base64_image(image_data: str) -> bytes:
    """
    解码Base64图片数据

    Args:
        image_data: Base64编码的图片数据，支持data URL格式

    Returns:
        图片字节数据

    Raises:
        ValueError: Base64数据格式错误或解码失败
    """
    try:
        # 处理data URL格式: data:image/jpeg;base64,/9j/4AAQ...
        if image_data.startswith('data:'):
            # 提取base64部分
            match = re.match(r'data:image/[^;]+;base64,(.+)', image_data)
            if not match:
                raise ValueError("无效的data URL格式，应为: data:image/type;base64,...")

            base64_data = match.group(1)

            # 验证图片类型
            image_type_match = re.match(r'data:image/([^;]+);base64,', image_data)
            if image_type_match:
                image_type = image_type_match.group(1).lower()
                supported_types = ['jpeg', 'jpg', 'png', 'gif', 'bmp', 'webp', 'mpo']
                if image_type not in supported_types:
                    raise ValueError(f"不支持的图片类型: {image_type}，支持的类型: {', '.join(supported_types)}")
        else:
            # 纯base64数据
            base64_data = image_data

        # 移除可能的空白字符
        base64_data = re.sub(r'\s+', '', base64_data)

        # 解码base64数据
        image_bytes = base64.b64decode(base64_data)

        # 验证是否为有效的图片数据（检查文件头）
        if not _is_valid_image(image_bytes):
            raise ValueError("解码后的数据不是有效的图片格式")

        return image_bytes

    except binascii.Error as e:
        raise ValueError(f"Base64解码失败: {str(e)}")
    except Exception as e:
        raise ValueError(f"图片数据处理失败: {str(e)}")


def _is_valid_image(image_bytes: bytes) -> bool:
    """
    检查字节数据是否为有效的图片格式

    Args:
        image_bytes: 图片字节数据

    Returns:
        是否为有效图片
    """
    if len(image_bytes) < 8:
        return False

    # 检查常见图片格式的文件头
    image_signatures = [
        b'\xFF\xD8\xFF',  # JPEG
        b'\x89PNG\r\n\x1a\n',  # PNG
        b'GIF87a',  # GIF87a
        b'GIF89a',  # GIF89a
        b'BM',  # BMP
        b'RIFF',  # WebP (需要进一步检查)
        b'\xFF\xD8\xFF',  # MPO (使用JPEG文件头，但需要进一步检查)
    ]

    for signature in image_signatures:
        if image_bytes.startswith(signature):
            return True

    # 特殊检查WebP格式
    if image_bytes.startswith(b'RIFF') and b'WEBP' in image_bytes[:12]:
        return True

    # 特殊检查MPO格式
    # MPO文件使用JPEG文件头，但在EXIF数据中包含MPO标识
    if image_bytes.startswith(b'\xFF\xD8\xFF') and len(image_bytes) > 100:
        # 检查是否包含MPO相关的EXIF标记
        if b'MPF\x00' in image_bytes[:1024] or b'0100' in image_bytes[:1024]:
            return True

    return False


@router.post("/calculate", response_model=ScoreCalculationResponse)
async def calculate_score(
    *,
    db: Session = Depends(deps.get_db),
    file: UploadFile = File(..., description="要评分的图片文件"),
    config_id: Optional[int] = Form(None, description="权重配置ID"),
    character_name: Optional[str] = Form(None, description="角色名称"),
    situation: Optional[str] = Form(None, description="情境标识符（可选，不提供则自动判断）"),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    上传图片并计算评分

    Args:
        db: 数据库会话
        file: 上传的图片文件
        config_id: 权重配置ID（可选，与character_name二选一）
        character_name: 角色名称（可选，与config_id二选一）
        situation: 情境标识符（可选，不提供则自动判断）
        current_user: 当前认证用户

    Returns:
        评分计算结果

    Raises:
        HTTPException: 各种验证失败时抛出相应错误
    """
    # 验证参数：config_id和character_name必须提供其中一个
    if not config_id and not character_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="必须提供config_id或character_name其中一个参数"
        )

    if config_id and character_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="config_id和character_name不能同时提供，请选择其中一个"
        )

    # 如果提供的是角色名字，转换为config_id
    if character_name:
        config = character_service.find_config_by_name_or_alias(db, character_name)

        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到角色'{character_name}'的配置，请先导入角色配置。支持的别名请查看character_aliases.json文件"
            )

        config_id = config.id

    # 验证文件类型
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型: {file.content_type}，仅支持图片文件"
        )
    
    # 验证配置和情境
    is_valid, error_msg, config = score_service.validate_config_and_situation(
        db, config_id, situation
    )
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 计算评分
        result = await score_service.calculate_score_from_image(
            db=db,
            image_bytes=file_content,
            config_id=config_id,
            situation=situation
        )
        
        return result
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"评分计算失败: {str(e)}"
        )


@router.post("/calculate-base64", response_model=ScoreCalculationResponse)
async def calculate_score_base64(
    *,
    db: Session = Depends(deps.get_db),
    request: Base64ImageRequest,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    使用Base64编码图片计算评分

    Args:
        db: 数据库会话
        request: Base64图片请求数据
        current_user: 当前认证用户

    Returns:
        评分计算结果

    Raises:
        HTTPException: 各种验证失败时抛出相应错误
    """
    # 验证参数：config_id和character_name必须提供其中一个
    if not request.config_id and not request.character_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="必须提供config_id或character_name其中一个参数"
        )

    if request.config_id and request.character_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="config_id和character_name不能同时提供，请选择其中一个"
        )

    # 如果提供的是角色名字，转换为config_id
    config_id = request.config_id
    if request.character_name:
        config = character_service.find_config_by_name_or_alias(db, request.character_name)

        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到角色'{request.character_name}'的配置，请先导入角色配置。支持的别名请查看character_aliases.json文件"
            )

        config_id = config.id

    # 验证配置和情境
    is_valid, error_msg, config = score_service.validate_config_and_situation(
        db, config_id, request.situation
    )
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )

    try:
        # 解析Base64图片数据
        image_bytes = _decode_base64_image(request.image_data)

        # 计算评分
        result = await score_service.calculate_score_from_image(
            db=db,
            image_bytes=image_bytes,
            config_id=config_id,
            situation=request.situation
        )

        return result

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"评分计算失败: {str(e)}"
        )


@router.get("/configs/{config_id}/situations")
def get_config_situations(
    *,
    db: Session = Depends(deps.get_db),
    config_id: int,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取指定配置支持的所有情境
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        current_user: 当前认证用户
        
    Returns:
        情境列表
        
    Raises:
        HTTPException: 配置不存在时抛出404错误
    """
    situations = score_service.get_config_situations(db, config_id)
    if not situations:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"权重配置不存在: {config_id}"
        )
    
    return {"config_id": config_id, "situations": situations}


@router.post("/preview")
async def preview_calculation(
    *,
    db: Session = Depends(deps.get_db),
    config_id: int = Form(..., description="权重配置ID"),
    situation: str = Form(..., description="情境标识符"),
    mock_terms: str = Form(..., description="模拟词条数据（JSON格式）"),
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    预览计算结果（仅管理员可用，用于测试配置）
    
    Args:
        db: 数据库会话
        config_id: 权重配置ID
        situation: 情境标识符
        mock_terms: 模拟词条数据（JSON字符串）
        current_user: 当前管理员用户
        
    Returns:
        预览计算结果
        
    Raises:
        HTTPException: 各种验证失败时抛出相应错误
    """
    import json
    
    try:
        # 解析模拟词条数据
        terms_data = json.loads(mock_terms)
        
        # 验证数据格式
        if not isinstance(terms_data, list):
            raise ValueError("模拟词条数据必须是数组格式")
        
        for term in terms_data:
            if not isinstance(term, dict) or not all(
                key in term for key in ["term_name", "source_text", "extracted_value"]
            ):
                raise ValueError("每个词条必须包含 term_name, source_text, extracted_value 字段")
        
        # 预览计算
        result = score_service.preview_calculation(
            db=db,
            config_id=config_id,
            situation=situation,
            mock_terms=terms_data
        )
        
        return result
        
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="模拟词条数据格式错误，必须是有效的JSON"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"预览计算失败: {str(e)}"
        )
