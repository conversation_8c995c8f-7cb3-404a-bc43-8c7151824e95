# 词条判断逻辑优化总结

## 问题背景

用户反馈了词条判断逻辑的具体要求：
1. 词条由2个主词条和5个副词条构成
2. 3种情况（4、3、1）主词条范围和权重不同，副词条权重相同
3. 属性伤害加成是统称，包括湮灭伤害加成等，只在情况3中出现，固定30%
4. 需要根据词条种类和数值自动判断情况
5. 处理OCR噪声字符，进行关键字匹配
6. 验证词条数值是否满级

## 优化方案

### 1. 自动情况判断逻辑

根据主词条的种类和数值自动判断声骸情况：

#### COST4声骸（情况4）特征
- 暴击: 22.0%
- 暴击伤害: 44.0%
- 治疗效果加成: 26.4%
- 生命%: 33.0%
- 防御%: 41.8%
- 攻击%: 33.0%
- 攻击: 150（固定值）

#### COST3声骸（情况3）特征
- 属性伤害加成: 30.0%（固定值）
- 共鸣效率: 32.0%
- 生命%: 30.0%
- 防御%: 38.0%
- 攻击%: 30.0%
- 攻击: 100（固定值）

#### COST1声骸（情况1）特征
- 生命: 2280（固定值）
- 生命%: 22.8%
- 防御%: 18.0%
- 攻击%: 18.0%

### 2. 智能词条名称匹配

#### OCR噪声处理
- 移除常见符号：`#`、`·`、`•`、`-`、`+`、`*`、空格
- 处理百分号后缀：`暴击%` → `暴击`
- 关键字模糊匹配

#### 属性伤害加成统一
所有具体属性伤害加成统一为"属性伤害加成"：
- 湮灭伤害加成 → 属性伤害加成
- 热熔伤害加成 → 属性伤害加成
- 导电伤害加成 → 属性伤害加成
- 气动伤害加成 → 属性伤害加成
- 衍射伤害加成 → 属性伤害加成
- 冷凝伤害加成 → 属性伤害加成

#### 暴击相关统一
- 暴击率 → 暴击
- 暴击% → 暴击

### 3. 词条数值验证

#### 满级数值表
**主词条数值（按情况区分）：**
- COST4: 暴击22.0%, 暴击伤害44.0%, 攻击%33.0%等
- COST3: 属性伤害加成30.0%, 共鸣效率32.0%, 攻击%30.0%等
- COST1: 生命2280, 生命%22.8%, 攻击%18.0%等

**副词条数值（所有情况相同）：**
- 攻击%: 11.6%, 攻击: 50
- 生命%: 11.6%, 生命: 320
- 防御%: 14.8%, 防御: 40
- 暴击: 8.4%, 暴击伤害: 16.8%
- 共鸣解放伤害加成: 12.6%
- 共鸣技能伤害加成: 12.6%
- 普攻伤害加成: 12.6%
- 重击伤害加成: 12.6%
- 共鸣效率: 6.8%

#### 验证逻辑
1. 允许5%误差范围
2. 优先匹配有效结果（主词条或副词条）
3. 如果数值不匹配，返回"词条数值未满"错误

### 4. API接口优化

#### 请求参数
- `situation`参数改为可选，不提供则自动判断
- 支持通过`config_id`或`character_name`指定配置

#### 响应增强
新增字段：
- `validation_errors`: 词条验证错误列表
- `main_props_found`: 识别到的主词条列表
- `sub_props_found`: 识别到的副词条列表
- `auto_detected_situation`: 自动检测的情况

## 实现细节

### 核心方法

#### 1. `_auto_determine_situation()`
```python
def _auto_determine_situation(self, parsed_terms: List[Dict]) -> str:
    """根据解析的词条自动判断情况（4、3、1）"""
    # 检查主词条特征，返回匹配度最高的情况
    # 如果没有明确特征，默认返回"3"
```

#### 2. `_clean_and_match_term_name()`
```python
def _clean_and_match_term_name(self, term_name: str) -> str:
    """清理词条名称并匹配到标准词条名"""
    # 移除OCR噪声，统一属性伤害加成，关键字匹配
```

#### 3. `_validate_term_value()`
```python
def _validate_term_value(self, term_name: str, value: float, situation: str) -> Dict:
    """验证词条数值是否满级"""
    # 检查主词条和副词条数值，返回验证结果
```

#### 4. `_calculate_score_enhanced()`
```python
def _calculate_score_enhanced(self, config, situation, parsed_terms) -> Dict:
    """增强的评分计算逻辑"""
    # 集成所有优化功能的评分计算
```

## 测试验证

### 测试用例
1. **自动情况检测**：✅ 通过
   - COST4声骸（暴击22.0%）→ 情况4
   - COST3声骸（属性伤害加成30.0%）→ 情况3
   - COST1声骸（生命2280）→ 情况1

2. **词条名称清理**：✅ 通过
   - `#暴击伤害` → `暴击伤害`
   - `湮灭伤害加成` → `属性伤害加成`
   - `共鸣解放伤害加成%` → `共鸣解放伤害加成`

3. **数值验证**：✅ 通过
   - 满级主词条识别
   - 满级副词条识别
   - 未满级词条检测
   - 未知词条处理

## 使用示例

### 自动判断情况
```bash
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -F "file=@image.jpg" \
  -F "config_id=43"
  # 不需要指定situation，系统自动判断
```

### 响应示例
```json
{
  "total_score": 45.23,
  "calculation_context": {
    "situation": "3",
    "auto_detected": true
  },
  "breakdown": [
    {
      "term_name": "属性伤害加成",
      "term_type": "main_prop",
      "source_text": "湮灭伤害加成 30.0%",
      "extracted_value": 30.0,
      "weight": 0.8,
      "score": 16.2
    },
    {
      "term_name": "共鸣解放伤害加成",
      "term_type": "sub_prop",
      "source_text": "共鸣解放伤害加成 12.6%",
      "extracted_value": 12.6,
      "weight": 0.165,
      "score": 14.18
    }
  ],
  "main_props_found": ["属性伤害加成"],
  "sub_props_found": ["共鸣解放伤害加成", "攻击%", "暴击"],
  "validation_errors": [],
  "auto_detected_situation": "3"
}
```

## 总结

通过这次优化，系统现在具备了：

1. **智能化**：自动判断声骸情况，无需手动指定
2. **准确性**：正确处理OCR噪声，统一属性伤害加成
3. **完整性**：验证词条数值是否满级，提供详细错误信息
4. **灵活性**：支持各种词条变体和格式
5. **可靠性**：区分主词条和副词条，避免权重混淆

这些改进完全满足了用户提出的词条判断逻辑要求，大大提升了评分系统的准确性和用户体验。
