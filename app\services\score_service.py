"""
评分计算服务
实现复杂的评分计算逻辑，包括权重应用和分数归一化
"""

from typing import Dict, List, Optional, Tuple

from sqlalchemy.orm import Session

from app.crud import weight_config
from app.models.weight_config import WeightConfig
from app.schemas.score import (
    CalculationContext, OCRResult, ScoreCalculationResponse, TermBreakdown
)
from app.services.ocr_service import ocr_service


class ScoreCalculationService:
    """评分计算服务类"""
    
    def __init__(self):
        """初始化评分计算服务"""
        self.aligned_score = 50.0  # 对齐分数，固定为50
    
    async def calculate_score_from_image(
        self,
        db: Session,
        image_bytes: bytes,
        config_id: int,
        situation: str = None
    ) -> ScoreCalculationResponse:
        """
        从图片计算评分

        Args:
            db: 数据库会话
            image_bytes: 图片字节数据
            config_id: 权重配置ID
            situation: 情境标识符（可选，如果不提供则自动判断）

        Returns:
            评分计算响应

        Raises:
            ValueError: 配置不存在或情境无效
        """
        # 1. 获取权重配置
        config = weight_config.get(db, id=config_id)
        if not config:
            raise ValueError(f"权重配置不存在: {config_id}")
        
        # 2. 解析词条（先解析，用于自动判断情况）
        # 验证图片
        is_valid, error_msg = ocr_service.validate_image(image_bytes)
        if not is_valid:
            raise ValueError(f"图片验证失败: {error_msg}")

        # OCR识别
        ocr_result = ocr_service.extract_text_from_image(image_bytes)

        # 解析词条
        parsed_terms = ocr_service.parse_terms_from_text(ocr_result["raw_text"])

        # 3. 自动判断情况（如果没有指定或为空字符串）
        logger.info(f"🔍 检查situation参数: '{situation}' (类型: {type(situation)})")
        if situation is None or situation == "" or situation.strip() == "":
            logger.info("📋 开始自动判断COST...")
            detected_cost = self._auto_determine_situation(parsed_terms)
            # 将COST数字映射到配置中的键格式
            situation = self._map_cost_to_config_key(config, detected_cost)
            logger.info(f"✅ 自动判断COST: {detected_cost} → 配置键: {situation}")
        else:
            logger.info(f"📌 使用指定的situation: '{situation}'")

        # 4. 验证情境是否存在
        if situation not in config.situation_map:
            available_situations = list(config.situation_map.keys())
            raise ValueError(f"情境 '{situation}' 在配置中不存在，可用情境: {available_situations}")

        # 5. 计算评分
        calculation_result = self._calculate_score_enhanced(config, situation, parsed_terms)
        
        # 6. 构建响应
        auto_detected = situation if situation != calculation_result.get("detected_situation") else None

        return ScoreCalculationResponse(
            total_score=calculation_result["total_score"],
            calculation_context=CalculationContext(
                config_name=config.name,
                config_id=config.id,
                situation=situation,
                max_unaligned_score=calculation_result["max_unaligned_score"],
                aligned_score=self.aligned_score
            ),
            breakdown=calculation_result["breakdown"],
            ocr_result=OCRResult(
                raw_text=ocr_result["raw_text"],
                confidence=ocr_result["confidence"],
                processing_time=ocr_result["processing_time"]
            ),
            valid_terms_count=calculation_result["valid_terms_count"],
            invalid_terms=calculation_result["invalid_terms"],
            validation_errors=calculation_result.get("validation_errors", []),
            main_props_found=calculation_result.get("main_props_found", []),
            sub_props_found=calculation_result.get("sub_props_found", []),
            auto_detected_situation=auto_detected
        )
    
    def _calculate_score(
        self, 
        config: WeightConfig, 
        situation: str, 
        parsed_terms: List[Dict]
    ) -> Dict:
        """
        执行核心评分计算逻辑
        
        Args:
            config: 权重配置
            situation: 情境标识符
            parsed_terms: 解析出的词条列表
            
        Returns:
            计算结果字典
        """
        # 获取情境对应的最高分
        situation_index = config.situation_map[situation]
        max_unaligned_score = config.score_max[situation_index]
        
        # 合并主词条和副词条权重
        combined_weights = self._combine_weights(config, situation)
        
        # 计算每个词条的得分
        breakdown = []
        total_score = 0.0
        valid_terms_count = 0
        invalid_terms = []
        
        for term in parsed_terms:
            term_name = term["term_name"]
            term_value = term["extracted_value"]

            # 根据数值判断词条类型
            actual_term_type = self._determine_term_type_by_value(term_name, term_value)

            # 根据实际词条类型选择权重
            weight = None
            if actual_term_type == "main_prop" and situation in config.main_props:
                # 主词条：从对应情境的主词条权重中获取
                if term_name in config.main_props[situation]:
                    weight = config.main_props[situation][term_name]
            elif actual_term_type == "sub_prop":
                # 副词条：从副词条权重中获取
                if term_name in config.sub_props:
                    weight = config.sub_props[term_name]

            if weight is not None:
                # 有效词条，计算得分
                # 核心计算公式：
                # 词条得分 = (词条数值 × 当前词条权重 ÷ 当前情境的未对齐最高分) × 对齐分数(50)
                term_score = (
                    term_value * weight / max_unaligned_score
                ) * self.aligned_score

                breakdown.append(TermBreakdown(
                    term_name=term_name,
                    term_type=actual_term_type,
                    source_text=term["source_text"],
                    extracted_value=term_value,
                    weight=weight,
                    score=round(term_score, 2)
                ))

                total_score += term_score
                valid_terms_count += 1
            else:
                # 无效词条，记录原始词条名
                invalid_terms.append(term_name if term_name else term.get("source_text", "未知词条"))
        
        return {
            "total_score": round(total_score, 2),
            "max_unaligned_score": max_unaligned_score,
            "breakdown": breakdown,
            "valid_terms_count": valid_terms_count,
            "invalid_terms": invalid_terms
        }
    
    def _combine_weights(self, config: WeightConfig, situation: str) -> Dict[str, Dict]:
        """
        合并主词条和副词条权重
        
        Args:
            config: 权重配置
            situation: 情境标识符
            
        Returns:
            合并后的权重字典
        """
        combined_weights = {}
        
        # 添加当前情境的主词条权重
        if situation in config.main_props:
            for term_name, weight in config.main_props[situation].items():
                combined_weights[term_name] = {
                    "weight": weight,
                    "type": "main_prop"
                }
        
        # 添加副词条权重（所有情境通用）
        for term_name, weight in config.sub_props.items():
            combined_weights[term_name] = {
                "weight": weight,
                "type": "sub_prop"
            }
        
        return combined_weights

    def _determine_term_type_by_value(self, term_name: str, value: float) -> str:
        """
        根据词条数值判断是主词条还是副词条

        Args:
            term_name: 词条名称
            value: 词条数值

        Returns:
            词条类型: "main_prop" 或 "sub_prop"
        """
        # 主词条数值范围定义
        main_prop_ranges = {
            # COST4主词条
            "攻击": [150],  # 固定值
            "暴击率": [22.0],  # 百分比
            "暴击": [22.0],  # 百分比，与暴击率相同
            "暴击伤害": [44.0],  # 百分比
            "治疗效果加成": [26.4],  # 百分比
            "生命%": [33.0],  # 百分比
            "防御%": [41.8],  # 百分比
            "攻击%": [33.0],  # 百分比

            # COST3主词条
            "属性伤害加成": [30.0],  # 百分比，包括各种元素伤害
            "共鸣效率": [32.0],  # 百分比

            # COST1主词条
            "生命": [2280],  # 固定值
        }

        # 副词条数值范围定义
        sub_prop_ranges = {
            "攻击": [30, 40, 50, 60],  # 固定值
            "防御": [40, 50, 60, 70],  # 固定值
            "生命": [320, 360, 390, 430, 470, 510, 540, 580],  # 固定值
            "攻击%": [6.46, 7.1, 7.9, 8.6, 9.4, 10.1, 10.9, 11.6],  # 百分比
            "防御%": [8.1, 9.0, 10.0, 10.9, 11.8, 12.8, 13.8, 14.7],  # 百分比
            "生命%": [6.4, 7.1, 7.9, 8.6, 9.4, 10.1, 10.9, 11.6],  # 百分比
            "暴击率": [6.3, 6.9, 7.5, 8.1, 8.7, 9.3, 9.9, 10.5],  # 百分比
            "暴击": [6.3, 6.9, 7.5, 8.1, 8.7, 9.3, 9.9, 10.5],  # 百分比，与暴击率相同
            "暴击伤害": [12.6, 13.8, 15.0, 16.2, 17.4, 18.6, 19.8, 21.0],  # 百分比
            "共鸣效率": [6.8, 7.6, 8.4, 9.2, 10.0, 10.8, 11.6, 12.4],  # 百分比
            "共鸣技能伤害加成": [6.4, 7.1, 7.9, 8.6, 9.4, 10.1, 10.9, 11.6],  # 百分比
            "共鸣解放伤害加成": [6.4, 7.1, 7.9, 8.6, 9.4, 10.1, 10.9, 11.6],  # 百分比
            "普攻伤害加成": [6.4, 7.1, 7.9, 8.6, 9.4, 10.1, 10.9, 11.6],  # 百分比
            "重击伤害加成": [6.4, 7.1, 7.9, 8.6, 9.4, 10.1, 10.9, 11.6],  # 百分比
        }

        # 首先检查是否为主词条
        if term_name in main_prop_ranges:
            main_values = main_prop_ranges[term_name]
            # 允许一定的误差范围（±5%）
            for main_value in main_values:
                if abs(value - main_value) <= main_value * 0.05:
                    return "main_prop"

        # 检查是否为副词条
        if term_name in sub_prop_ranges:
            sub_values = sub_prop_ranges[term_name]
            # 允许一定的误差范围（±5%）
            for sub_value in sub_values:
                if abs(value - sub_value) <= sub_value * 0.05:
                    return "sub_prop"

        # 默认返回副词条（大部分词条都是副词条）
        return "sub_prop"

    def validate_config_and_situation(
        self, db: Session, config_id: int, situation: str
    ) -> Tuple[bool, str, Optional[WeightConfig]]:
        """
        验证配置和情境的有效性
        
        Args:
            db: 数据库会话
            config_id: 配置ID
            situation: 情境标识符
            
        Returns:
            (是否有效, 错误信息, 配置对象)
        """
        # 检查配置是否存在
        config = weight_config.get(db, id=config_id)
        if not config:
            return False, f"权重配置不存在: {config_id}", None
        
        # 检查配置是否激活
        if config.is_active != "active":
            return False, f"权重配置未激活: {config_id}", None
        
        # 检查情境是否存在（如果提供了情境）
        if situation is not None and situation not in config.situation_map:
            available_situations = list(config.situation_map.keys())
            return False, f"情境 '{situation}' 不存在，可用情境: {available_situations}", None
        
        return True, "", config

    def _map_cost_to_config_key(self, config: WeightConfig, cost: str) -> str:
        """
        将COST数字映射到配置中的实际键

        Args:
            config: 权重配置对象
            cost: COST数字 ("4", "3", "1")

        Returns:
            配置中的实际键
        """
        # 常见的映射模式
        cost_mappings = [
            f"c{cost}",      # c4, c3, c1
            f"cost{cost}",   # cost4, cost3, cost1
            cost,            # 4, 3, 1
            f"C{cost}",      # C4, C3, C1
            f"COST{cost}",   # COST4, COST3, COST1
        ]

        # 查找匹配的键
        for mapping in cost_mappings:
            if mapping in config.situation_map:
                logger.debug(f"COST映射: {cost} → {mapping}")
                return mapping

        # 如果没有找到精确匹配，尝试模糊匹配
        available_keys = list(config.situation_map.keys())
        for key in available_keys:
            if cost in key.lower() or key.lower() in cost:
                logger.debug(f"COST模糊映射: {cost} → {key}")
                return key

        # 如果都没有找到，返回第一个可用的键并记录警告
        if available_keys:
            fallback_key = available_keys[0]
            logger.warning(f"COST映射失败，使用默认键: {cost} → {fallback_key}")
            return fallback_key

        # 最后的兜底，直接返回原始COST
        logger.error(f"无法映射COST到配置键: {cost}")
        return cost

    def get_config_situations(self, db: Session, config_id: int) -> List[str]:
        """
        获取配置支持的所有情境
        
        Args:
            db: 数据库会话
            config_id: 配置ID
            
        Returns:
            情境列表
        """
        config = weight_config.get(db, id=config_id)
        if not config:
            return []
        
        return list(config.situation_map.keys())
    
    def preview_calculation(
        self, db: Session, config_id: int, situation: str, mock_terms: List[Dict]
    ) -> Dict:
        """
        预览计算结果（用于测试和调试）
        
        Args:
            db: 数据库会话
            config_id: 配置ID
            situation: 情境标识符
            mock_terms: 模拟词条数据
            
        Returns:
            预览计算结果
        """
        config = weight_config.get(db, id=config_id)
        if not config:
            raise ValueError(f"权重配置不存在: {config_id}")
        
        if situation not in config.situation_map:
            raise ValueError(f"情境 '{situation}' 在配置中不存在")
        
        return self._calculate_score_enhanced(config, situation, mock_terms)

    def _auto_determine_situation(self, parsed_terms: List[Dict]) -> str:
        """
        根据解析的词条自动判断情况（4、3、1）
        优先判断固定词条，再判断变动词条

        Args:
            parsed_terms: 解析出的词条列表

        Returns:
            情况标识符: "4", "3", "1"
        """
        logger.info("开始自动判断COST情况...")

        # 第一步：优先检查固定词条（最可靠的判断依据）
        fixed_indicators = {
            "4": {"攻击": 150},      # C4固定：攻击150
            "3": {"攻击": 100},      # C3固定：攻击100
            "1": {"生命": 2280}      # C1固定：生命2280
        }

        for term in parsed_terms:
            term_name = term["term_name"]
            term_value = term["extracted_value"]

            # 检查是否匹配固定词条
            for situation, fixed_props in fixed_indicators.items():
                if term_name in fixed_props:
                    expected_value = fixed_props[term_name]
                    # 允许5%误差
                    if abs(term_value - expected_value) <= expected_value * 0.05:
                        logger.info(f"✅ 通过固定词条判断COST: {term_name}={term_value} → C{situation}")
                        return situation

        logger.info("未找到固定词条，开始检查变动主词条...")

        # 第二步：如果没有固定词条，检查变动主词条
        variable_indicators = {
            "4": {
                # C4变动主词条（优先级：独有 > 高数值）
                "暴击": [22.0],           # C4独有
                "暴击伤害": [44.0],       # C4独有
                "治疗效果加成": [26.4],   # C4独有
                "生命%": [33.0],          # C4最高
                "防御%": [41.8],          # C4最高
                "攻击%": [33.0]           # C4最高
            },
            "3": {
                # C3变动主词条（优先级：独有 > 特殊数值）
                "属性伤害加成": [30.0],   # C3独有，固定30%
                "共鸣效率": [32.0],       # C3独有
                "生命%": [30.0],          # C3特有数值
                "防御%": [38.0],          # C3特有数值
                "攻击%": [30.0]           # C3特有数值
            },
            "1": {
                # C1变动主词条（数值较低）
                "生命%": [22.8],          # C1特有数值
                "防御%": [18.0],          # C1特有数值
                "攻击%": [18.0]           # C1特有数值
            }
        }

        # 计算每个情况的匹配度
        situation_scores = {"4": 0, "3": 0, "1": 0}
        matched_terms = {"4": [], "3": [], "1": []}

        for term in parsed_terms:
            term_name = term["term_name"]
            term_value = term["extracted_value"]

            # 检查变动主词条
            for situation, indicators in variable_indicators.items():
                if term_name in indicators:
                    expected_values = indicators[term_name]
                    # 检查数值是否匹配（允许5%误差）
                    for expected_value in expected_values:
                        if abs(term_value - expected_value) <= expected_value * 0.05:
                            situation_scores[situation] += 1
                            matched_terms[situation].append(f"{term_name}={term_value}")
                            logger.debug(f"变动词条匹配: C{situation} - {term_name}={term_value}")
                            break

        # 第三步：特殊规则和优先级处理

        # 规则1：属性伤害加成30%是C3的强特征（几乎100%确定）
        for term in parsed_terms:
            if term["term_name"] == "属性伤害加成" and abs(term["extracted_value"] - 30.0) <= 1.5:
                logger.info(f"✅ 检测到属性伤害加成30%，强制判断为C3")
                return "3"

        # 规则2：独有词条优先（C4独有词条权重最高）
        c4_exclusive = ["暴击", "暴击伤害", "治疗效果加成"]
        c3_exclusive = ["共鸣效率"]

        for term in parsed_terms:
            term_name = term["term_name"]
            term_value = term["extracted_value"]

            # C4独有词条检查
            if term_name in c4_exclusive:
                for expected_value in variable_indicators["4"].get(term_name, []):
                    if abs(term_value - expected_value) <= expected_value * 0.05:
                        logger.info(f"✅ 检测到C4独有词条: {term_name}={term_value} → C4")
                        return "4"

            # C3独有词条检查
            if term_name in c3_exclusive:
                for expected_value in variable_indicators["3"].get(term_name, []):
                    if abs(term_value - expected_value) <= expected_value * 0.05:
                        logger.info(f"✅ 检测到C3独有词条: {term_name}={term_value} → C3")
                        return "3"

        # 规则3：基于匹配度和置信度的综合判断
        if max(situation_scores.values()) > 0:
            # 如果有明显的优势（得分差距>=2），直接返回
            sorted_scores = sorted(situation_scores.items(), key=lambda x: x[1], reverse=True)
            best_situation, best_score = sorted_scores[0]
            second_score = sorted_scores[1][1] if len(sorted_scores) > 1 else 0

            if best_score >= 2 or (best_score > 0 and best_score - second_score >= 2):
                logger.info(f"✅ 通过变动词条判断COST: C{best_situation} (得分: {best_score}, 匹配词条: {matched_terms[best_situation]})")
                return best_situation

            # 如果得分接近，使用优先级规则：C4 > C3 > C1
            if best_score > 0:
                priority_order = ["4", "3", "1"]
                for priority_situation in priority_order:
                    if situation_scores[priority_situation] > 0:
                        logger.info(f"✅ 通过优先级规则判断COST: C{priority_situation} (得分: {situation_scores[priority_situation]}, 匹配词条: {matched_terms[priority_situation]})")
                        return priority_situation

        # 如果都没有匹配，默认返回C4
        logger.info("⚠️ 未找到明确的主词条特征，默认判断为C4")
        return "4"

    def _calculate_score_enhanced(
        self,
        config: WeightConfig,
        situation: str,
        parsed_terms: List[Dict]
    ) -> Dict:
        """
        增强的评分计算逻辑，包含词条验证和错误处理

        Args:
            config: 权重配置
            situation: 情境标识符
            parsed_terms: 解析出的词条列表

        Returns:
            计算结果字典
        """
        # 获取情境对应的最高分
        situation_index = config.situation_map[situation]
        max_unaligned_score = config.score_max[situation_index]

        # 分析词条结构
        main_props_found = []
        sub_props_found = []
        invalid_terms = []
        validation_errors = []

        breakdown = []
        total_score = 0.0
        valid_terms_count = 0

        for term in parsed_terms:
            term_name = term["term_name"]
            term_value = term["extracted_value"]
            source_text = term["source_text"]

            # 清理词条名称，处理OCR的额外字符
            cleaned_term_name = self._clean_and_match_term_name(term_name)

            # 验证词条数值是否满级
            validation_result = self._validate_term_value(cleaned_term_name, term_value, situation)

            if validation_result["is_valid"]:
                term_type = validation_result["term_type"]
                weight = None

                # 根据词条类型获取权重
                if term_type == "main_prop" and situation in config.main_props:
                    if cleaned_term_name in config.main_props[situation]:
                        weight = config.main_props[situation][cleaned_term_name]
                        main_props_found.append(cleaned_term_name)
                elif term_type == "sub_prop":
                    if cleaned_term_name in config.sub_props:
                        weight = config.sub_props[cleaned_term_name]
                        sub_props_found.append(cleaned_term_name)

                if weight is not None:
                    # 计算得分
                    term_score = (
                        term_value * weight / max_unaligned_score
                    ) * self.aligned_score

                    breakdown.append(TermBreakdown(
                        term_name=cleaned_term_name,
                        term_type=term_type,
                        source_text=source_text,
                        extracted_value=term_value,
                        weight=weight,
                        score=round(term_score, 2)
                    ))

                    total_score += term_score
                    valid_terms_count += 1
                else:
                    # 词条有效但配置中没有权重
                    invalid_terms.append(f"{cleaned_term_name}(配置中无权重)")
            else:
                # 词条无效或数值不满级
                if validation_result["error_type"] == "not_max_level":
                    validation_errors.append(f"{cleaned_term_name}: 词条数值未满")
                else:
                    invalid_terms.append(cleaned_term_name or source_text)

        # 构建结果
        result = {
            "total_score": round(total_score, 2),
            "max_unaligned_score": max_unaligned_score,
            "breakdown": breakdown,
            "valid_terms_count": valid_terms_count,
            "invalid_terms": invalid_terms,
            "validation_errors": validation_errors,
            "main_props_found": main_props_found,
            "sub_props_found": sub_props_found,
            "detected_situation": situation
        }

        return result

    def _clean_and_match_term_name(self, term_name: str) -> str:
        """
        清理词条名称并匹配到标准词条名
        处理OCR可能产生的额外字符和符号

        Args:
            term_name: 原始词条名称

        Returns:
            清理后的标准词条名称
        """
        if not term_name:
            return ""

        # 移除常见的OCR噪声字符
        import re
        cleaned = re.sub(r'[#·•\-\+\*\s]+', '', term_name)

        # 词条名称映射表，处理各种可能的OCR结果
        term_mapping = {
            # 基础属性
            "攻击": "攻击",
            "攻击%": "攻击%",
            "生命": "生命",
            "生命%": "生命%",
            "防御": "防御",
            "防御%": "防御%",

            # 暴击相关
            "暴击": "暴击",
            "暴击%": "暴击",  # 暴击统一为"暴击"
            "暴击率": "暴击",
            "暴击伤害": "暴击伤害",
            "暴击伤害%": "暴击伤害",

            # 伤害加成类
            "普攻伤害加成": "普攻伤害加成",
            "普攻伤害加成%": "普攻伤害加成",
            "重击伤害加成": "重击伤害加成",
            "重击伤害加成%": "重击伤害加成",
            "共鸣技能伤害加成": "共鸣技能伤害加成",
            "共鸣技能伤害加成%": "共鸣技能伤害加成",
            "共鸣解放伤害加成": "共鸣解放伤害加成",
            "共鸣解放伤害加成%": "共鸣解放伤害加成",

            # 属性伤害加成（统称）
            "属性伤害加成": "属性伤害加成",
            "湮灭伤害加成": "属性伤害加成",
            "热熔伤害加成": "属性伤害加成",
            "导电伤害加成": "属性伤害加成",
            "气动伤害加成": "属性伤害加成",
            "衍射伤害加成": "属性伤害加成",
            "冷凝伤害加成": "属性伤害加成",

            # 其他
            "共鸣效率": "共鸣效率",
            "共鸣效率%": "共鸣效率",
            "治疗效果加成": "治疗效果加成",
            "治疗效果加成%": "治疗效果加成",
            "元素精通": "元素精通"
        }

        # 尝试直接匹配
        if cleaned in term_mapping:
            return term_mapping[cleaned]

        # 尝试模糊匹配（去掉%后缀）
        cleaned_no_percent = cleaned.rstrip('%')
        if cleaned_no_percent in term_mapping:
            return term_mapping[cleaned_no_percent]

        # 尝试关键字匹配
        for key, value in term_mapping.items():
            if key in cleaned or cleaned in key:
                return value

        # 如果都没匹配到，返回清理后的名称
        return cleaned_no_percent

    def _validate_term_value(self, term_name: str, value: float, situation: str) -> Dict:
        """
        验证词条数值是否满级

        Args:
            term_name: 词条名称
            value: 词条数值
            situation: 情况标识符

        Returns:
            验证结果字典
        """
        # 定义满级数值表（基于词条数值.md）
        max_values = {
            # 主词条数值
            "main_props": {
                "4": {
                    "暴击": 22.0,
                    "暴击伤害": 44.0,
                    "治疗效果加成": 26.4,
                    "生命%": 33.0,
                    "防御%": 41.8,
                    "攻击%": 33.0,
                    "攻击": 150
                },
                "3": {
                    "属性伤害加成": 30.0,
                    "共鸣效率": 32.0,
                    "生命%": 30.0,
                    "防御%": 38.0,
                    "攻击%": 30.0,
                    "攻击": 100
                },
                "1": {
                    "生命": 2280,
                    "生命%": 22.8,
                    "防御%": 18.0,
                    "攻击%": 18.0
                }
            },
            # 副词条数值（所有情况下都相同，支持多个强化等级）
            "sub_props": {
                "攻击%": [11.6, 10.9, 10.1, 9.4, 8.6, 7.9, 7.1, 6.46],
                "攻击": [60, 50, 40, 30],
                "生命%": [11.6, 10.9, 10.1, 9.4, 8.6, 7.9, 7.1, 6.4],
                "生命": [580, 540, 510, 470, 430, 390, 360, 320],
                "防御%": [14.7, 13.8, 12.8, 11.8, 10.9, 10.0, 9.0, 8.1],
                "防御": [70, 60, 50, 40],
                "暴击": [10.5, 9.9, 9.3, 8.7, 8.1, 7.5, 6.9, 6.3],
                "暴击伤害": [21.0, 19.8, 18.6, 17.4, 16.2, 15.0, 13.8, 12.6],
                "共鸣解放伤害加成": [11.6, 10.9, 10.1, 9.4, 8.6, 7.9, 7.1, 6.4],
                "共鸣技能伤害加成": [11.6, 10.9, 10.1, 9.4, 8.6, 7.9, 7.1, 6.4],
                "普攻伤害加成": [11.6, 10.9, 10.1, 9.4, 8.6, 7.9, 7.1, 6.4],
                "重击伤害加成": [11.6, 10.9, 10.1, 9.4, 8.6, 7.9, 7.1, 6.4],
                "共鸣效率": [12.4, 11.6, 10.8, 10.0, 9.2, 8.4, 7.6, 6.8]
            }
        }

        # 允许的误差范围（5%）
        tolerance = 0.05

        # 检查是否为主词条
        main_prop_result = None
        if situation in max_values["main_props"]:
            main_props = max_values["main_props"][situation]
            if term_name in main_props:
                expected_value = main_props[term_name]
                if abs(value - expected_value) <= expected_value * tolerance:
                    main_prop_result = {
                        "is_valid": True,
                        "term_type": "main_prop",
                        "expected_value": expected_value,
                        "matched_value": expected_value,
                        "error_type": None
                    }
                else:
                    main_prop_result = {
                        "is_valid": False,
                        "term_type": "main_prop",
                        "expected_value": expected_value,
                        "matched_value": None,
                        "error_type": "not_max_level"
                    }

        # 检查是否为副词条（支持多个数值）
        sub_prop_result = None
        if term_name in max_values["sub_props"]:
            possible_values = max_values["sub_props"][term_name]
            matched_value = None

            # 检查是否匹配任何一个可能的数值
            for expected_value in possible_values:
                if abs(value - expected_value) <= expected_value * tolerance:
                    matched_value = expected_value
                    break

            if matched_value is not None:
                sub_prop_result = {
                    "is_valid": True,
                    "term_type": "sub_prop",
                    "expected_value": possible_values,  # 所有可能值
                    "matched_value": matched_value,     # 匹配到的值
                    "error_type": None
                }
            else:
                sub_prop_result = {
                    "is_valid": False,
                    "term_type": "sub_prop",
                    "expected_value": possible_values,
                    "matched_value": None,
                    "error_type": "not_max_level"
                }

        # 优先返回有效的结果
        if main_prop_result and main_prop_result["is_valid"]:
            return main_prop_result
        if sub_prop_result and sub_prop_result["is_valid"]:
            return sub_prop_result

        # 如果都无效，优先返回主词条结果（如果存在）
        if main_prop_result:
            return main_prop_result
        if sub_prop_result:
            return sub_prop_result

        # 未知词条
        return {
            "is_valid": False,
            "term_type": "unknown",
            "expected_value": None,
            "error_type": "unknown_term"
        }


# 创建全局评分计算服务实例
score_service = ScoreCalculationService()
