# 用户管理指南

本文档介绍智能评分API系统中的用户管理功能，包括用户创建、管理和权限控制。

## 用户角色

系统支持两种用户角色：

### 管理员 (admin)
- 可以管理所有用户（创建、查看、更新、删除）
- 可以管理权重配置
- 可以访问所有API端点
- 具有系统的完全控制权

### 普通用户 (user)
- 可以使用评分功能
- 可以查看角色信息
- 只能访问基本的API端点
- 不能管理其他用户或系统配置

## 初始用户

系统初始化时会自动创建以下用户：

### 管理员账户
- **用户名**: `admin`
- **密码**: `admin123`
- **邮箱**: `<EMAIL>`
- **角色**: `admin`

### 测试用户账户
- **用户名**: `testuser`
- **密码**: `test123`
- **邮箱**: `<EMAIL>`
- **角色**: `user`

## 创建用户的方法

### 1. 使用数据库初始化脚本

运行初始化脚本会创建默认用户：

```bash
python init_db.py
```

### 2. 使用简单命令行脚本（推荐）

使用不依赖外部库的简单脚本：

```bash
# 创建普通用户（交互式输入密码）
python simple_create_user.py create newuser -e <EMAIL>

# 创建管理员用户
python simple_create_user.py create newadmin -e <EMAIL> -r admin

# 创建未激活用户
python simple_create_user.py create inactiveuser -e <EMAIL> --inactive

# 直接指定密码（不推荐）
python simple_create_user.py create quickuser -p password123 -e <EMAIL>

# 列出所有用户
python simple_create_user.py list
```

### 3. 使用完整功能脚本（需要依赖）

如果已安装所有依赖包，可以使用功能更完整的脚本：

```bash
# 创建普通用户（交互式输入密码）
python create_user.py newuser -e <EMAIL>

# 创建管理员用户
python create_user.py newadmin -e <EMAIL> -r admin

# 创建未激活用户
python create_user.py inactiveuser -e <EMAIL> --inactive

# 直接指定密码（不推荐）
python create_user.py quickuser -p password123 -e <EMAIL>
```

### 3. 使用API端点

通过HTTP API创建用户（需要管理员权限）：

```bash
# 首先获取管理员令牌
curl -X POST "http://localhost:8000/api/v1/auth/login-json" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'

# 使用令牌创建用户
curl -X POST "http://localhost:8000/api/v1/users/" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "apiuser",
    "email": "<EMAIL>",
    "password": "secure_password",
    "role": "user",
    "is_active": true
  }'
```

## 用户管理API

### 创建用户
- **端点**: `POST /api/v1/users/`
- **权限**: 仅管理员
- **请求体**: UserCreate模式

### 获取用户列表
- **端点**: `GET /api/v1/users/`
- **权限**: 仅管理员
- **参数**: skip（跳过记录数）, limit（返回记录数）

### 获取单个用户
- **端点**: `GET /api/v1/users/{user_id}`
- **权限**: 仅管理员

### 更新用户
- **端点**: `PUT /api/v1/users/{user_id}`
- **权限**: 仅管理员
- **请求体**: UserUpdate模式

### 删除用户
- **端点**: `DELETE /api/v1/users/{user_id}`
- **权限**: 仅管理员
- **限制**: 不能删除自己的账户

## 用户数据模式

### UserCreate（用户创建）
```json
{
  "username": "string",      // 必填，3-50字符
  "email": "string",         // 可选，有效邮箱格式
  "password": "string",      // 必填，6-100字符
  "role": "user",           // 可选，"admin"或"user"
  "is_active": true         // 可选，默认true
}
```

### UserUpdate（用户更新）
```json
{
  "username": "string",      // 可选
  "email": "string",         // 可选
  "password": "string",      // 可选，更新密码
  "role": "string",         // 可选
  "is_active": true         // 可选
}
```

### UserResponse（用户响应）
```json
{
  "id": 1,
  "username": "string",
  "email": "string",
  "role": "string",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

## 安全注意事项

1. **密码安全**
   - 密码使用bcrypt进行哈希存储
   - 最小长度为6位
   - 建议使用强密码

2. **权限控制**
   - 用户管理功能仅限管理员访问
   - 使用JWT令牌进行身份验证
   - 管理员不能删除自己的账户

3. **数据验证**
   - 用户名和邮箱必须唯一
   - 输入数据经过严格验证
   - 防止SQL注入和其他安全漏洞

## 常见问题

### Q: 如何重置管理员密码？
A: 可以通过数据库直接修改，或者使用init_db.py脚本重新初始化。

### Q: 如何批量创建用户？
A: 可以编写脚本调用create_user.py，或者直接操作数据库。

### Q: 用户忘记密码怎么办？
A: 目前系统没有密码重置功能，管理员可以为用户重新设置密码。

### Q: 如何禁用用户？
A: 管理员可以通过API将用户的is_active设置为false。

## 实际使用示例

### 快速开始

1. **查看现有用户**：
   ```bash
   python simple_create_user.py list
   ```

2. **创建新的普通用户**：
   ```bash
   python simple_create_user.py create john -e <EMAIL>
   # 系统会提示输入密码
   ```

3. **创建管理员用户**：
   ```bash
   python simple_create_user.py create manager -e <EMAIL> -r admin
   ```

### 当前系统用户

根据测试，系统当前包含以下用户：

| ID | 用户名 | 邮箱 | 角色 | 状态 |
|----|--------|------|------|------|
| 1 | admin | <EMAIL> | admin | 激活 |
| 2 | testuser | <EMAIL> | user | 激活 |
| 3 | newuser | <EMAIL> | user | 激活 |
| 4 | myadmin | <EMAIL> | admin | 激活 |

## 相关文件

- `app/models/user.py` - 用户数据模型
- `app/schemas/user.py` - 用户数据模式
- `app/crud/crud_user.py` - 用户CRUD操作
- `app/api/v1/endpoints/users.py` - 用户管理API
- `create_user.py` - 完整功能用户创建脚本（需要依赖）
- `simple_create_user.py` - 简单用户创建脚本（无依赖）
- `init_db.py` - 数据库初始化脚本
