"""
认证相关API端点
处理用户登录、令牌验证等认证功能
"""

from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.api import deps
from app.core.config import settings
from app.core.security import create_access_token
from app.crud import user
from app.models.user import User
from app.schemas.token import Token, LoginRequest
from app.schemas.user import UserResponse

router = APIRouter()


@router.post("/login", response_model=Token)
def login_for_access_token(
    db: Session = Depends(deps.get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    用户登录获取访问令牌（OAuth2兼容）
    
    Args:
        db: 数据库会话
        form_data: OAuth2密码表单数据
        
    Returns:
        访问令牌
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    # 验证用户凭据
    authenticated_user = user.authenticate(
        db, username=form_data.username, password=form_data.password
    )
    if not authenticated_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户是否激活
    if not user.is_active(authenticated_user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=authenticated_user.username, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
    }


@router.post("/login-json", response_model=Token)
def login_with_json(
    login_data: LoginRequest,
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    用户登录获取访问令牌（JSON格式）
    
    Args:
        login_data: 登录请求数据
        db: 数据库会话
        
    Returns:
        访问令牌
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    # 验证用户凭据
    authenticated_user = user.authenticate(
        db, username=login_data.username, password=login_data.password
    )
    if not authenticated_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户是否激活
    if not user.is_active(authenticated_user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=authenticated_user.username, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
    }


@router.post("/test-token", response_model=UserResponse)
def test_token(
    current_user: User = Depends(deps.get_current_user)
) -> Any:
    """
    测试访问令牌有效性
    
    Args:
        current_user: 当前认证用户
        
    Returns:
        当前用户信息
    """
    return current_user


@router.get("/me", response_model=UserResponse)
def read_users_me(
    current_user: User = Depends(deps.get_current_user)
) -> Any:
    """
    获取当前用户信息
    
    Args:
        current_user: 当前认证用户
        
    Returns:
        当前用户信息
    """
    return current_user
