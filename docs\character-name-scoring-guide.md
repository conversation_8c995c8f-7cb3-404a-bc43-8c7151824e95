# 角色名字评分功能使用指南

## 功能概述

Smart Scoring API 现在支持使用角色名字进行评分计算，无需记住复杂的config_id。

## 新增功能

### 1. 角色列表包含Config ID

**端点**: `GET /api/v1/characters/list`

**新的响应格式**:
```json
[
  {
    "character_name": "卡提希娅",
    "config_id": 7,
    "config_name": "卡提希娅-通用",
    "has_config": true
  },
  {
    "character_name": "今汐",
    "config_id": 15,
    "config_name": "今汐-通用配置",
    "has_config": true
  },
  {
    "character_name": "安可",
    "config_id": null,
    "config_name": null,
    "has_config": false
  }
]
```

**字段说明**:
- `character_name`: 角色名称
- `config_id`: 数据库中的配置ID（如果已导入）
- `config_name`: 配置的完整名称
- `has_config`: 是否已导入到数据库

### 2. 评分计算支持角色名字

**端点**: `POST /api/v1/scores/calculate`

**新的参数选项**:
- `config_id` (可选): 权重配置ID
- `character_name` (可选): 角色名称
- **注意**: `config_id` 和 `character_name` 必须提供其中一个，不能同时提供

## 使用方式

### 方式一：使用Config ID（原有方式）

```bash
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.png" \
  -F "config_id=7" \
  -F "situation=4"
```

### 方式二：使用角色名字（新功能）

```bash
curl -X POST "http://localhost:8000/api/v1/scores/calculate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.png" \
  -F "character_name=卡提希娅" \
  -F "situation=4"
```

### PowerShell示例

```powershell
# 获取角色列表
$token = "YOUR_ACCESS_TOKEN"
$headers = @{ "Authorization" = "Bearer $token" }
$characters = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/characters/list" -Headers $headers

# 显示角色信息
$characters | ForEach-Object {
    Write-Host "$($_.character_name): Config ID = $($_.config_id)"
}

# 使用角色名字评分
$form = @{
    file = Get-Item "image.png"
    character_name = "卡提希娅"
    situation = "4"
}
$result = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/scores/calculate" -Method Post -Form $form -Headers $headers
```

## API文档中的使用

1. **访问API文档**: http://localhost:8000/api/v1/docs

2. **获取角色列表**:
   - 找到 `/characters/list` 端点
   - 执行后查看返回的角色信息和config_id

3. **评分计算**:
   - 找到 `/scores/calculate` 端点
   - 现在可以选择填写 `config_id` 或 `character_name`
   - 两个参数只能选择其中一个

## 错误处理

### 常见错误

1. **参数缺失**:
```json
{
  "detail": "必须提供config_id或character_name其中一个参数"
}
```

2. **参数冲突**:
```json
{
  "detail": "config_id和character_name不能同时提供，请选择其中一个"
}
```

3. **角色未找到**:
```json
{
  "detail": "未找到角色'不存在的角色'的配置，请先导入角色配置"
}
```

## 工作流程

### 推荐的使用流程

1. **获取角色列表**:
   ```
   GET /api/v1/characters/list
   ```

2. **选择角色**:
   - 查看 `has_config: true` 的角色
   - 记录 `character_name` 或 `config_id`

3. **进行评分**:
   - 使用 `character_name` (推荐，更直观)
   - 或使用 `config_id` (原有方式)

### 自动化脚本示例

```python
import requests

# 登录获取token
token = login_and_get_token()
headers = {"Authorization": f"Bearer {token}"}

# 获取可用角色
response = requests.get("http://localhost:8000/api/v1/characters/list", headers=headers)
characters = response.json()

# 筛选已导入的角色
available_chars = [c for c in characters if c['has_config']]
print("可用角色:")
for char in available_chars:
    print(f"  {char['character_name']} (ID: {char['config_id']})")

# 使用角色名字评分
with open("image.png", "rb") as f:
    files = {"file": ("image.png", f, "image/png")}
    data = {
        "character_name": "卡提希娅",  # 直接使用角色名字
        "situation": "4"
    }
    result = requests.post(
        "http://localhost:8000/api/v1/scores/calculate",
        files=files,
        data=data,
        headers=headers
    )
```

## 优势

### 使用角色名字的优势

1. **更直观**: 不需要记住数字ID
2. **更易用**: 角色名字比数字更容易记忆
3. **更灵活**: 即使重新导入配置，角色名字保持不变
4. **向后兼容**: 原有的config_id方式仍然可用

### 适用场景

- **新用户**: 推荐使用角色名字，更容易上手
- **自动化脚本**: 使用角色名字，代码更易读
- **API集成**: 角色名字作为参数更直观
- **现有系统**: 可以继续使用config_id，无需修改

## 注意事项

1. **角色必须已导入**: 只有 `has_config: true` 的角色才能用于评分
2. **名字匹配**: 角色名字必须与配置文件夹名完全一致
3. **中文支持**: 完全支持中文角色名字
4. **大小写敏感**: 角色名字区分大小写

现在您可以更方便地使用角色名字进行评分计算了！🎉
