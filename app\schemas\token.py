"""
JWT令牌相关的数据模式
定义登录请求、令牌响应等数据结构
"""

from pydantic import BaseModel, Field


class Token(BaseModel):
    """访问令牌响应模式"""
    
    access_token: str = Field(..., description="JWT访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")


class TokenPayload(BaseModel):
    """令牌载荷模式"""
    
    sub: str = Field(..., description="令牌主体（用户名）")
    exp: int = Field(..., description="过期时间戳")


class LoginRequest(BaseModel):
    """登录请求模式"""
    
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    password: str = Field(..., min_length=6, max_length=100, description="密码")
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "admin",
                "password": "secure_password"
            }
        }
