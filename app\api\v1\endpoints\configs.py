"""
权重配置管理API端点
处理权重配置的CRUD操作，仅限管理员访问
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.api import deps
from app.crud import weight_config
from app.models.user import User
from app.schemas.weight_config import (
    WeightConfigCreate, WeightConfigResponse, WeightConfigUpdate,
    WeightConfigListResponse
)

router = APIRouter()


@router.post("/", response_model=WeightConfigResponse, status_code=status.HTTP_201_CREATED)
def create_config(
    *,
    db: Session = Depends(deps.get_db),
    config_in: WeightConfigCreate,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    创建新的权重配置
    
    Args:
        db: 数据库会话
        config_in: 权重配置创建数据
        current_user: 当前管理员用户
        
    Returns:
        创建的权重配置
        
    Raises:
        HTTPException: 配置名称已存在时抛出400错误
    """
    # 检查配置名称是否已存在
    existing_config = weight_config.get_by_name(db, name=config_in.name)
    if existing_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"配置名称 '{config_in.name}' 已存在"
        )
    
    # 创建配置
    config = weight_config.create(db, obj_in=config_in)
    return config


@router.get("/", response_model=WeightConfigListResponse)
def read_configs(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status_filter: str = Query(None, pattern="^(active|inactive)$", description="状态筛选"),
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    获取权重配置列表
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        status_filter: 状态筛选
        current_user: 当前管理员用户
        
    Returns:
        权重配置列表
    """
    if status_filter:
        configs = weight_config.get_by_status(
            db, status=status_filter, skip=skip, limit=limit
        )
        total = len(configs)  # 简化实现，实际应该查询总数
    else:
        configs = weight_config.get_multi(db, skip=skip, limit=limit)
        total = weight_config.count(db)
    
    return WeightConfigListResponse(
        configs=configs,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/{config_id}", response_model=WeightConfigResponse)
def read_config(
    *,
    db: Session = Depends(deps.get_db),
    config_id: int,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    获取单个权重配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        current_user: 当前管理员用户
        
    Returns:
        权重配置详情
        
    Raises:
        HTTPException: 配置不存在时抛出404错误
    """
    config = weight_config.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"权重配置不存在: {config_id}"
        )
    return config


@router.put("/{config_id}", response_model=WeightConfigResponse)
def update_config(
    *,
    db: Session = Depends(deps.get_db),
    config_id: int,
    config_in: WeightConfigUpdate,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    更新权重配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        config_in: 权重配置更新数据
        current_user: 当前管理员用户
        
    Returns:
        更新后的权重配置
        
    Raises:
        HTTPException: 配置不存在时抛出404错误，名称冲突时抛出400错误
    """
    config = weight_config.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"权重配置不存在: {config_id}"
        )
    
    # 如果更新名称，检查是否与其他配置冲突
    if config_in.name and config_in.name != config.name:
        existing_config = weight_config.get_by_name(db, name=config_in.name)
        if existing_config and existing_config.id != config_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"配置名称 '{config_in.name}' 已存在"
            )
    
    config = weight_config.update(db, db_obj=config, obj_in=config_in)
    return config


@router.delete("/{config_id}", response_model=WeightConfigResponse)
def delete_config(
    *,
    db: Session = Depends(deps.get_db),
    config_id: int,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    删除权重配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        current_user: 当前管理员用户
        
    Returns:
        删除的权重配置
        
    Raises:
        HTTPException: 配置不存在时抛出404错误
    """
    config = weight_config.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"权重配置不存在: {config_id}"
        )
    
    config = weight_config.remove(db, id=config_id)
    return config


@router.post("/{config_id}/activate", response_model=WeightConfigResponse)
def activate_config(
    *,
    db: Session = Depends(deps.get_db),
    config_id: int,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    激活权重配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        current_user: 当前管理员用户
        
    Returns:
        激活后的权重配置
        
    Raises:
        HTTPException: 配置不存在时抛出404错误
    """
    config = weight_config.activate_config(db, config_id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"权重配置不存在: {config_id}"
        )
    return config


@router.post("/{config_id}/deactivate", response_model=WeightConfigResponse)
def deactivate_config(
    *,
    db: Session = Depends(deps.get_db),
    config_id: int,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    停用权重配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        current_user: 当前管理员用户
        
    Returns:
        停用后的权重配置
        
    Raises:
        HTTPException: 配置不存在时抛出404错误
    """
    config = weight_config.deactivate_config(db, config_id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"权重配置不存在: {config_id}"
        )
    return config


@router.get("/{config_id}/situations", response_model=List[str])
def get_config_situations(
    *,
    db: Session = Depends(deps.get_db),
    config_id: int,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    获取配置支持的所有情境
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        current_user: 当前管理员用户
        
    Returns:
        情境列表
        
    Raises:
        HTTPException: 配置不存在时抛出404错误
    """
    config = weight_config.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"权重配置不存在: {config_id}"
        )
    
    return list(config.situation_map.keys())
