#!/usr/bin/env python3
"""
Smart Scoring API 简化启动脚本
避免编码问题的简单启动方式
"""

import os
import sys
import time


def print_banner():
    """打印项目横幅"""
    print("=" * 60)
    print("Smart Scoring API - 智能评分系统")
    print("基于 FastAPI + PaddleOCR 的图片评分后端服务")
    print("=" * 60)
    print()


def check_files():
    """检查必要文件"""
    print("检查项目文件...")
    
    required_files = [
        'main.py',
        'pyproject.toml',
        '.env'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"缺少必要文件: {', '.join(missing_files)}")
        if '.env' in missing_files and os.path.exists('.env.example'):
            print("正在创建 .env 文件...")
            import shutil
            shutil.copy('.env.example', '.env')
            print("已创建 .env 文件")
        return False
    
    print("必要文件检查通过")
    return True


def start_with_python():
    """使用Python直接启动"""
    print("\n正在启动API服务...")
    print("请稍等，服务正在初始化...")
    
    try:
        # 设置环境变量避免编码问题
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # 导入并启动应用
        import uvicorn
        
        print("启动 FastAPI 服务...")
        print("服务地址: http://localhost:8000")
        print("API文档: http://localhost:8000/api/v1/docs")
        print("按 Ctrl+C 停止服务")
        print("-" * 50)
        
        # 启动服务
        uvicorn.run(
            "main:app",
            host="127.0.0.1",
            port=8000,
            reload=False,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保已安装所有依赖:")
        print("  pip install -r requirements.txt")
        print("或")
        print("  poetry install")
        return False
    except Exception as e:
        print(f"启动失败: {e}")
        return False
    
    return True


def start_with_docker():
    """使用Docker启动（简化版）"""
    print("\n使用Docker启动服务...")
    
    # 检查Docker是否可用
    if not os.system("docker --version >nul 2>&1") == 0:
        print("Docker未安装或未启动")
        return False
    
    print("正在启动服务...")
    
    # 启动服务
    result = os.system("docker-compose up -d")
    
    if result == 0:
        print("服务启动成功!")
        print("等待服务就绪...")
        time.sleep(15)
        
        print("\n服务信息:")
        print("- API服务: http://localhost:8000")
        print("- API文档: http://localhost:8000/api/v1/docs")
        print("- 健康检查: http://localhost:8000/health")
        
        print("\n停止服务: docker-compose down")
        return True
    else:
        print("Docker启动失败")
        return False


def show_usage():
    """显示使用说明"""
    print("\n使用说明:")
    print("1. Python直接启动:")
    print("   python start_simple.py")
    print("   python main.py")
    print("   uvicorn main:app --host 127.0.0.1 --port 8000")
    print()
    print("2. Docker启动:")
    print("   docker-compose up -d")
    print()
    print("3. 开发模式:")
    print("   uvicorn main:app --host 127.0.0.1 --port 8000 --reload")
    print()
    print("默认账户:")
    print("- 管理员: admin / admin123")
    print("- 用户: testuser / test123")


def main():
    """主函数"""
    print_banner()
    
    # 检查文件
    if not check_files():
        print("请确保在项目根目录下运行此脚本")
        sys.exit(1)
    
    # 询问启动方式
    print("选择启动方式:")
    print("1. Python直接启动 (推荐)")
    print("2. Docker启动")
    print("3. 显示使用说明")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1" or choice == "":
            success = start_with_python()
        elif choice == "2":
            success = start_with_docker()
        elif choice == "3":
            show_usage()
            return
        else:
            print("无效选择")
            show_usage()
            return
        
        if not success:
            print("\n启动失败，请查看错误信息")
            show_usage()
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n启动过程被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n启动过程中出现错误: {e}")
        show_usage()
        sys.exit(1)


if __name__ == "__main__":
    main()
