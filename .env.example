# 数据库配置 (可选，未配置时将禁用数据库功能)
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name
# DATABASE_URL=sqlite:///./smartscoring.db
DATABASE_URL=

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
PROJECT_NAME="Smart Scoring API"
VERSION="1.0.0"
DEBUG=true
API_V1_STR="/api/v1"

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg,image/mpo

# OCR配置
OCR_USE_GPU=false
OCR_LANG=ch

# 安全配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
