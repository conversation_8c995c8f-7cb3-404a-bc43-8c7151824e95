@echo off
setlocal enabledelayedexpansion

echo ============================================================
echo Smart Scoring API - Local Docker Build Script
echo ============================================================
echo.

echo Checking Docker environment...
docker --version >nul 2>&1
if errorlevel 1 (
    echo Error: Docker not installed or not running
    pause
    exit /b 1
)

echo Docker environment OK
echo.

echo Choose build method:
echo 1. Use local Dockerfile (recommended)
echo 2. Use original Dockerfile
echo 3. Database services only
echo.

set /p choice="Please choose (1-3): "

if "%choice%"=="1" goto local_build
if "%choice%"=="2" goto original_build
if "%choice%"=="3" goto db_only
if "%choice%"=="" goto local_build

echo Invalid choice, using default...
goto local_build

:local_build
echo.
echo Building image with local Dockerfile...
echo This uses domestic mirror sources to avoid network issues
echo.

docker build -f Dockerfile.local -t smartscoring-api:local .

if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

echo.
echo Build successful! Starting services...
docker-compose -f docker-compose.local.yml up -d

goto end

:original_build
echo.
echo Building with original Dockerfile...
echo Note: May encounter network issues
echo.

docker-compose build

if errorlevel 1 (
    echo Build failed, recommend using local build method
    pause
    exit /b 1
)

echo.
echo Build successful! Starting services...
docker-compose up -d

goto end

:db_only
echo.
echo Starting database services only...
echo API service will run with Python directly
echo.

docker-compose up -d db redis

if errorlevel 1 (
    echo Failed to start database services
    pause
    exit /b 1
)

echo.
echo Database services started successfully!
echo Now you can start API with:
echo   python main.py
echo.

goto end

:end
echo.
echo Service Information:
echo - API Service: http://localhost:8000
echo - API Documentation: http://localhost:8000/api/v1/docs
echo.
echo Stop services: docker-compose down
echo.
pause
