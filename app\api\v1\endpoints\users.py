"""
用户管理API端点
处理用户的创建、查询、更新、删除等操作
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.crud import user
from app.models.user import User
from app.schemas.user import (
    UserCreate, UserUpdate, UserResponse, UserListResponse
)

router = APIRouter()


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
def create_user(
    *,
    db: Session = Depends(deps.get_db),
    user_in: UserCreate,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    创建新用户（仅管理员可操作）
    
    Args:
        db: 数据库会话
        user_in: 用户创建数据
        current_user: 当前管理员用户
        
    Returns:
        创建的用户信息
        
    Raises:
        HTTPException: 用户名或邮箱已存在时抛出400错误
    """
    # 检查用户名是否已存在
    existing_user = user.get_by_username(db, username=user_in.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"用户名 '{user_in.username}' 已存在"
        )
    
    # 检查邮箱是否已存在（如果提供了邮箱）
    if user_in.email:
        existing_email = user.get_by_email(db, email=user_in.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"邮箱 '{user_in.email}' 已存在"
            )
    
    # 创建用户
    created_user = user.create(db, obj_in=user_in)
    return created_user


@router.get("/", response_model=UserListResponse)
def read_users(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    获取用户列表（仅管理员可操作）
    
    Args:
        db: 数据库会话
        skip: 跳过的记录数
        limit: 返回的记录数
        current_user: 当前管理员用户
        
    Returns:
        用户列表和分页信息
    """
    users = user.get_multi(db, skip=skip, limit=limit)
    total = user.count(db)
    
    return UserListResponse(
        users=users,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/{user_id}", response_model=UserResponse)
def read_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    根据ID获取用户信息（仅管理员可操作）
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        current_user: 当前管理员用户
        
    Returns:
        用户信息
        
    Raises:
        HTTPException: 用户不存在时抛出404错误
    """
    db_user = user.get(db, id=user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return db_user


@router.put("/{user_id}", response_model=UserResponse)
def update_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    更新用户信息（仅管理员可操作）
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        user_in: 用户更新数据
        current_user: 当前管理员用户
        
    Returns:
        更新后的用户信息
        
    Raises:
        HTTPException: 用户不存在或用户名/邮箱冲突时抛出错误
    """
    db_user = user.get(db, id=user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查用户名冲突（如果要更新用户名）
    if user_in.username and user_in.username != db_user.username:
        existing_user = user.get_by_username(db, username=user_in.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"用户名 '{user_in.username}' 已存在"
            )
    
    # 检查邮箱冲突（如果要更新邮箱）
    if user_in.email and user_in.email != db_user.email:
        existing_email = user.get_by_email(db, email=user_in.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"邮箱 '{user_in.email}' 已存在"
            )
    
    # 更新用户
    updated_user = user.update(db, db_obj=db_user, obj_in=user_in)
    return updated_user


@router.delete("/{user_id}")
def delete_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    删除用户（仅管理员可操作）
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        current_user: 当前管理员用户
        
    Returns:
        删除成功消息
        
    Raises:
        HTTPException: 用户不存在或尝试删除自己时抛出错误
    """
    db_user = user.get(db, id=user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 防止管理员删除自己
    if db_user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户"
        )
    
    # 删除用户
    user.remove(db, id=user_id)
    return {"message": f"用户 '{db_user.username}' 已成功删除"}
