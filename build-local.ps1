# Smart Scoring API - Local Docker Build Script
# PowerShell version to avoid encoding issues

Write-Host "============================================================" -ForegroundColor Cyan
Write-Host "Smart Scoring API - Local Docker Build Script" -ForegroundColor Cyan
Write-Host "============================================================" -ForegroundColor Cyan
Write-Host ""

# Check Docker environment
Write-Host "Checking Docker environment..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "Docker environment OK: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Docker not installed or not running" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Choose build method:" -ForegroundColor Yellow
Write-Host "1. Use local Dockerfile (recommended)" -ForegroundColor White
Write-Host "2. Use original Dockerfile" -ForegroundColor White
Write-Host "3. Database services only" -ForegroundColor White
Write-Host "4. Direct Python startup (no Docker)" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Please choose (1-4, default: 1)"
if ([string]::IsNullOrEmpty($choice)) { $choice = "1" }

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "Building image with local Dockerfile..." -ForegroundColor Yellow
        Write-Host "This uses domestic mirror sources to avoid network issues" -ForegroundColor Gray
        Write-Host ""
        
        # Build local image
        docker build -f Dockerfile.local -t smartscoring-api:local .
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Build failed" -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit 1
        }
        
        Write-Host ""
        Write-Host "Build successful! Starting services..." -ForegroundColor Green
        docker-compose -f docker-compose.local.yml up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Services started successfully!" -ForegroundColor Green
        }
    }
    
    "2" {
        Write-Host ""
        Write-Host "Building with original Dockerfile..." -ForegroundColor Yellow
        Write-Host "Note: May encounter network issues" -ForegroundColor Red
        Write-Host ""
        
        docker-compose build
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Build failed, recommend using local build method" -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit 1
        }
        
        Write-Host ""
        Write-Host "Build successful! Starting services..." -ForegroundColor Green
        docker-compose up -d
    }
    
    "3" {
        Write-Host ""
        Write-Host "Starting database services only..." -ForegroundColor Yellow
        Write-Host "API service will run with Python directly" -ForegroundColor Gray
        Write-Host ""
        
        docker-compose up -d db redis
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Failed to start database services" -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit 1
        }
        
        Write-Host ""
        Write-Host "Database services started successfully!" -ForegroundColor Green
        Write-Host "Now you can start API with:" -ForegroundColor Yellow
        Write-Host "  python main.py" -ForegroundColor White
        Write-Host ""
    }
    
    "4" {
        Write-Host ""
        Write-Host "Starting API directly with Python..." -ForegroundColor Yellow
        Write-Host "No Docker containers will be used" -ForegroundColor Gray
        Write-Host ""
        
        # Check if .env exists
        if (-not (Test-Path ".env")) {
            if (Test-Path ".env.example") {
                Copy-Item ".env.example" ".env"
                Write-Host "Created .env file from template" -ForegroundColor Green
            }
        }
        
        Write-Host "Starting API service..." -ForegroundColor Yellow
        Write-Host "Service will be available at: http://localhost:8000" -ForegroundColor Cyan
        Write-Host "API Documentation: http://localhost:8000/api/v1/docs" -ForegroundColor Cyan
        Write-Host "Press Ctrl+C to stop the service" -ForegroundColor Gray
        Write-Host ""
        
        python main.py
        exit 0
    }
    
    default {
        Write-Host "Invalid choice, exiting..." -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "============================================================" -ForegroundColor Cyan
Write-Host "Service Information:" -ForegroundColor Yellow
Write-Host "- API Service: http://localhost:8000" -ForegroundColor White
Write-Host "- API Documentation: http://localhost:8000/api/v1/docs" -ForegroundColor White
Write-Host "- Health Check: http://localhost:8000/health" -ForegroundColor White
Write-Host ""
Write-Host "Default Accounts:" -ForegroundColor Yellow
Write-Host "- Admin: admin / admin123" -ForegroundColor White
Write-Host "- User: testuser / test123" -ForegroundColor White
Write-Host ""
Write-Host "Stop services: docker-compose down" -ForegroundColor Gray
Write-Host "============================================================" -ForegroundColor Cyan

Read-Host "Press Enter to exit"
