#!/usr/bin/env python3
"""
Smart Scoring API 启动脚本
用于快速启动和验证项目
"""

import os
import sys
import subprocess
import time


def print_banner():
    """打印项目横幅"""
    print("=" * 60)
    print("🚀 Smart Scoring API - 智能评分系统")
    print("   基于 FastAPI + PaddleOCR 的图片评分后端服务")
    print("=" * 60)
    print()


def check_python_version():
    """检查Python版本"""
    print("📋 检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.9或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_docker():
    """检查Docker是否可用"""
    print("\n🐳 检查Docker环境...")
    try:
        result = subprocess.run(['docker', '--version'],
                              capture_output=True, text=True, timeout=10, encoding='utf-8', errors='ignore')
        if result.returncode == 0:
            print(f"✅ {result.stdout.strip()}")

            # 检查Docker Compose
            result = subprocess.run(['docker-compose', '--version'],
                                  capture_output=True, text=True, timeout=10, encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                print(f"✅ {result.stdout.strip()}")
                return True
            else:
                print("❌ Docker Compose 未安装")
                return False
        else:
            print("❌ Docker 未安装或未启动")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Docker 未安装")
        return False


def create_env_file():
    """创建环境配置文件"""
    print("\n⚙️  配置环境变量...")
    
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ 已创建 .env 文件")
        else:
            print("❌ .env.example 文件不存在")
            return False
    else:
        print("✅ .env 文件已存在")
    
    return True


def start_with_docker():
    """使用Docker启动服务"""
    print("\n🚀 使用Docker启动服务...")
    
    try:
        # 启动服务
        print("正在启动数据库和缓存服务...")
        result = subprocess.run([
            'docker-compose', 'up', '-d', 'db', 'redis'
        ], capture_output=True, text=True, timeout=60, encoding='utf-8', errors='ignore')

        if result.returncode != 0:
            print(f"❌ 启动数据库服务失败: {result.stderr}")
            return False

        print("✅ 数据库和缓存服务启动成功")

        # 等待服务就绪
        print("等待服务就绪...")
        time.sleep(10)

        # 启动API服务
        print("正在启动API服务...")
        result = subprocess.run([
            'docker-compose', 'up', '-d', 'api'
        ], capture_output=True, text=True, timeout=120, encoding='utf-8', errors='ignore')

        if result.returncode != 0:
            print(f"❌ 启动API服务失败: {result.stderr}")
            return False
        
        print("✅ API服务启动成功")
        
        # 等待API服务就绪
        print("等待API服务就绪...")
        time.sleep(15)
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 服务启动超时")
        return False
    except Exception as e:
        print(f"❌ 启动服务时出错: {e}")
        return False


def verify_services():
    """验证服务是否正常运行"""
    print("\n🔍 验证服务状态...")
    
    try:
        import requests
        
        # 检查健康状态
        response = requests.get('http://localhost:8000/health', timeout=10)
        if response.status_code == 200:
            print("✅ API服务运行正常")
            data = response.json()
            print(f"   状态: {data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ API服务响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到API服务: {e}")
        return False
    except ImportError:
        print("⚠️  requests模块未安装，跳过服务验证")
        return True


def show_next_steps():
    """显示后续步骤"""
    print("\n🎉 服务启动完成！")
    print("\n📖 接下来您可以:")
    print("1. 访问API文档: http://localhost:8000/api/v1/docs")
    print("2. 查看ReDoc文档: http://localhost:8000/api/v1/redoc")
    print("3. 运行测试脚本: python test_api_manual.py")
    print("4. 查看服务日志: docker-compose logs -f api")
    print("\n🔑 默认账户信息:")
    print("   管理员 - 用户名: admin, 密码: admin123")
    print("   普通用户 - 用户名: testuser, 密码: test123")
    print("\n📚 更多信息请查看 README.md 和 docs/ 目录")
    print("\n⏹️  停止服务: docker-compose down")


def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_python_version():
        sys.exit(1)
    
    docker_available = check_docker()
    
    if not docker_available:
        print("\n❌ Docker环境不可用")
        print("请安装Docker和Docker Compose后重试")
        print("安装指南: https://docs.docker.com/get-docker/")
        sys.exit(1)
    
    # 配置环境
    if not create_env_file():
        sys.exit(1)
    
    # 启动服务
    if not start_with_docker():
        print("\n❌ 服务启动失败")
        print("请检查Docker服务状态和日志:")
        print("  docker-compose logs")
        sys.exit(1)
    
    # 验证服务
    if verify_services():
        show_next_steps()
    else:
        print("\n⚠️  服务可能未完全就绪，请稍等片刻后访问:")
        print("   http://localhost:8000/health")
        show_next_steps()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  启动过程被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 启动过程中出现错误: {e}")
        sys.exit(1)
