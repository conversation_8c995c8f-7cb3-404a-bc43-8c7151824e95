"""
权重配置数据模型
定义权重配置表结构，用于存储复杂的评分权重配置
"""

from sqlalchemy import Column, DateTime, Integer, String, Text, JSON
from sqlalchemy.sql import func

from app.db.base import Base


class WeightConfig(Base):
    """权重配置模型"""
    
    __tablename__ = "weight_configs"
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True)
    
    # 配置名称（唯一，用于人类识别）
    name = Column(String(100), unique=True, index=True, nullable=False)
    
    # 配置描述
    description = Column(Text, nullable=True)
    
    # 主词条权重配置（JSON格式存储复杂结构）
    # 结构: {"c4": {"词条A": 0.5, "词条B": 0.25}, "c1": {"词条A": 0.4}}
    main_props = Column(JSON, nullable=False)

    # 副词条权重配置（JSON格式）
    # 结构: {"词条C": 1.0, "词条D": 0.3}
    sub_props = Column(JSON, nullable=False)

    # 未对齐最高分列表（JSON格式）
    # 结构: [75.0, 80.0]
    score_max = Column(JSON, nullable=False)

    # 情境映射（JSON格式）
    # 结构: {"c4": 0, "c1": 1}
    situation_map = Column(JSON, nullable=False)
    
    # 是否激活
    is_active = Column(String(10), default="active", nullable=False)  # active, inactive
    
    # 创建时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 更新时间
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<WeightConfig(id={self.id}, name='{self.name}', is_active='{self.is_active}')>"
