# Smart Scoring API 项目结构

## 项目目录结构

```
smart-scoring-api/
├── app/                          # 应用程序主目录
│   ├── __init__.py
│   ├── api/                      # API路由
│   │   ├── __init__.py
│   │   ├── deps.py              # 依赖注入
│   │   └── v1/                  # API v1版本
│   │       ├── __init__.py
│   │       ├── api.py           # API路由汇总
│   │       └── endpoints/       # API端点
│   │           ├── __init__.py
│   │           ├── auth.py      # 认证相关
│   │           ├── characters.py # 角色配置
│   │           ├── configs.py   # 权重配置
│   │           └── scores.py    # 评分计算
│   ├── core/                    # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py           # 应用配置
│   │   └── security.py         # 安全相关
│   ├── crud/                    # 数据库操作
│   │   ├── __init__.py
│   │   ├── base.py             # 基础CRUD
│   │   ├── user.py             # 用户操作
│   │   └── weight_config.py    # 权重配置操作
│   ├── db/                      # 数据库相关
│   │   ├── __init__.py
│   │   ├── base.py             # 数据库基类
│   │   └── session.py          # 数据库会话
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py             # 用户模型
│   │   └── weight_config.py    # 权重配置模型
│   ├── schemas/                 # Pydantic模式
│   │   ├── __init__.py
│   │   ├── score.py            # 评分相关模式
│   │   ├── user.py             # 用户相关模式
│   │   └── weight_config.py    # 权重配置模式
│   └── services/                # 业务逻辑服务
│       ├── __init__.py
│       ├── character_service.py # 角色配置服务
│       ├── ocr_service.py      # OCR识别服务
│       └── score_service.py    # 评分计算服务
├── character/                   # 角色配置目录
│   ├── default/                # 默认配置
│   │   └── calc.json
│   ├── 今汐/                   # 角色配置
│   │   └── calc.json
│   ├── 安可/
│   │   └── calc.json
│   ├── 长离/
│   │   └── calc.json
│   ├── 维里奈/
│   │   └── calc.json
│   ├── 炽霞/
│   │   └── calc.json
│   ├── 卡提希娅/
│   │   └── calc.json
│   └── ... (更多角色)
├── docs/                        # 项目文档
│   ├── README.md               # 文档说明
│   ├── api-guide.md            # API使用指南
│   ├── character-aliases-guide.md # 角色别名指南
│   ├── character-config-guide.md  # 角色配置指南
│   ├── character-name-scoring-guide.md # 角色名评分指南
│   ├── deployment.md           # 部署指南
│   ├── project-structure.md    # 项目结构说明
│   ├── project-summary.md      # 项目技术总结
│   ├── 项目交付清单.md         # 交付清单
│   └── 项目实现总结.md         # 实现总结
├── tests/                       # 测试目录
│   ├── __init__.py
│   ├── conftest.py             # 测试配置
│   └── test_api/               # API测试
│       ├── __init__.py
│       ├── test_auth.py        # 认证测试
│       ├── test_configs.py     # 配置测试
│       └── test_scores.py      # 评分测试
├── .env                        # 环境变量配置
├── .env.example               # 环境变量示例
├── .gitignore                 # Git忽略文件
├── character_aliases.json     # 角色别名配置
├── docker-compose.dev.yml     # 开发环境Docker配置
├── docker-compose.yml         # 生产环境Docker配置
├── docker-entrypoint.sh       # Docker入口脚本
├── Dockerfile                 # Docker镜像构建文件
├── init_db.py                 # 数据库初始化脚本
├── main.py                    # 应用程序入口
├── nginx.conf                 # Nginx配置文件
├── poetry.lock                # Poetry锁定文件
├── pyproject.toml             # Python项目配置
├── README.md                  # 项目说明文档
├── requirements.txt           # Python依赖列表
├── smartscoring.db           # SQLite数据库文件
└── start.py                   # 启动脚本
```

## 核心文件说明

### 应用程序文件
- **main.py**: FastAPI应用程序的主入口点
- **start.py**: 便捷的启动脚本
- **init_db.py**: 数据库初始化和角色配置导入脚本

### 配置文件
- **.env**: 环境变量配置（包含敏感信息）
- **.env.example**: 环境变量配置模板
- **character_aliases.json**: 角色别名配置文件
- **pyproject.toml**: Python项目和依赖配置
- **requirements.txt**: Python依赖列表（用于非Poetry环境）

### Docker相关
- **Dockerfile**: Docker镜像构建配置
- **docker-compose.yml**: 生产环境容器编排
- **docker-compose.dev.yml**: 开发环境容器编排
- **docker-entrypoint.sh**: Docker容器启动脚本
- **nginx.conf**: Nginx反向代理配置

### 数据文件
- **smartscoring.db**: SQLite数据库文件
- **character/**: 角色配置文件目录，包含39+个角色的calc.json文件

## 目录功能说明

### `/app` - 应用程序核心
包含所有的业务逻辑、API端点、数据模型和服务。

### `/character` - 角色配置
存储所有角色的权重配置文件，每个角色一个子目录。

### `/docs` - 项目文档
包含完整的项目文档，包括API指南、部署说明、使用教程等。

### `/tests` - 测试代码
包含单元测试和集成测试，确保代码质量。

## 已清理的文件

以下测试和临时文件已被清理：
- 所有 `test_*.py` 测试脚本
- 所有 `demo_*.py` 演示脚本
- 所有 `simple_*.py` 简化测试脚本
- 所有 `*_output.txt` 和 `*_result.txt` 临时输出文件
- Python缓存目录 `__pycache__`

## Git忽略规则

`.gitignore` 文件已更新，包含以下忽略规则：
- Python缓存文件和目录
- 环境变量文件（.env）
- 数据库文件（开发环境）
- 测试和临时文件
- IDE和编辑器配置文件
- 日志文件
- 构建产物

## 项目特点

1. **清晰的分层架构**: API层、服务层、数据层分离
2. **完整的文档体系**: 从使用指南到技术文档一应俱全
3. **灵活的配置管理**: 支持环境变量和配置文件
4. **容器化部署**: 完整的Docker部署方案
5. **测试覆盖**: 完整的测试体系确保代码质量
6. **角色配置系统**: 支持39+个角色的自动配置管理
7. **别名系统**: 灵活的角色别名支持

这个项目结构遵循了现代Python Web应用的最佳实践，具有良好的可维护性和扩展性。
