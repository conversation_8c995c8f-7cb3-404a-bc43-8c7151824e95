@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ============================================================
echo Smart Scoring API - 智能评分系统
echo 基于 FastAPI + PaddleOCR 的图片评分后端服务
echo ============================================================
echo.

echo 检查项目文件...
if not exist "main.py" (
    echo 错误: 找不到 main.py 文件
    echo 请确保在项目根目录下运行此脚本
    pause
    exit /b 1
)

if not exist ".env" (
    if exist ".env.example" (
        echo 正在创建 .env 文件...
        copy ".env.example" ".env" >nul
        echo 已创建 .env 文件
    ) else (
        echo 警告: 找不到 .env 文件
    )
)

echo 文件检查完成
echo.

echo 选择启动方式:
echo 1. Python直接启动 (推荐)
echo 2. Docker启动
echo 3. 开发模式启动
echo.

set /p choice="请选择 (1-3): "

if "%choice%"=="1" goto python_start
if "%choice%"=="2" goto docker_start  
if "%choice%"=="3" goto dev_start
if "%choice%"=="" goto python_start

echo 无效选择，使用默认方式启动...
goto python_start

:python_start
echo.
echo 正在启动API服务...
echo 服务地址: http://localhost:8000
echo API文档: http://localhost:8000/api/v1/docs
echo 按 Ctrl+C 停止服务
echo ------------------------------------------------------------

set PYTHONIOENCODING=utf-8
python -m uvicorn main:app --host 127.0.0.1 --port 8000

if errorlevel 1 (
    echo.
    echo 启动失败，尝试安装依赖...
    pip install -r requirements.txt
    echo.
    echo 重新启动服务...
    python -m uvicorn main:app --host 127.0.0.1 --port 8000
)

goto end

:docker_start
echo.
echo 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker未安装或未启动
    echo 请安装Docker后重试
    pause
    exit /b 1
)

echo Docker环境正常
echo.
echo 正在启动服务...
docker-compose up -d

if errorlevel 1 (
    echo Docker启动失败
    pause
    exit /b 1
)

echo.
echo 服务启动成功!
echo 等待服务就绪...
timeout /t 15 /nobreak >nul

echo.
echo 服务信息:
echo - API服务: http://localhost:8000
echo - API文档: http://localhost:8000/api/v1/docs  
echo - 健康检查: http://localhost:8000/health
echo.
echo 停止服务: docker-compose down

goto end

:dev_start
echo.
echo 启动开发模式 (热重载)...
echo 服务地址: http://localhost:8000
echo API文档: http://localhost:8000/api/v1/docs
echo 按 Ctrl+C 停止服务
echo ------------------------------------------------------------

set PYTHONIOENCODING=utf-8
python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload

goto end

:end
echo.
echo 默认账户信息:
echo - 管理员: admin / admin123
echo - 用户: testuser / test123
echo.
pause
