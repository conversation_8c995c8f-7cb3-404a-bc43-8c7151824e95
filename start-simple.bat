@echo off
echo Smart Scoring API - Simple Startup
echo ====================================
echo.

echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    pause
    exit /b 1
)

echo Python environment OK
echo.

echo Checking project files...
if not exist "main.py" (
    echo Error: main.py not found
    echo Please run this script in the project root directory
    pause
    exit /b 1
)

if not exist ".env" (
    if exist ".env.example" (
        echo Creating .env file...
        copy ".env.example" ".env" >nul
        echo .env file created
    )
)

echo.
echo Starting API service...
echo Service URL: http://localhost:8000
echo API Docs: http://localhost:8000/api/v1/docs
echo Press Ctrl+C to stop
echo.

python main.py

pause
