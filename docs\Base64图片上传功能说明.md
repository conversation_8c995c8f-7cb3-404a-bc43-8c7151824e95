# Base64图片上传功能说明

## 功能概述

系统现在支持两种图片上传方式：
1. **传统文件上传**：通过multipart/form-data上传图片文件
2. **Base64上传**：通过JSON请求体上传Base64编码的图片数据

## API端点

### 1. 传统文件上传（现有功能）
```
POST /api/v1/scores/calculate
Content-Type: multipart/form-data
```

### 2. Base64图片上传（新增功能）
```
POST /api/v1/scores/calculate-base64
Content-Type: application/json
```

## Base64上传详细说明

### 请求格式

#### 请求体结构
```json
{
  "image_data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
  "config_id": 43,
  "situation": "3"
}
```

#### 参数说明
- **image_data** (必填): Base64编码的图片数据
- **config_id** (可选): 权重配置ID，与character_name二选一
- **character_name** (可选): 角色名称，与config_id二选一
- **situation** (可选): 情境标识符，不提供则自动判断

### 支持的图片格式

#### Data URL格式（推荐）
```
data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...
data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...
data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAAL...
```

#### 纯Base64格式
```
/9j/4AAQSkZJRgABAQEAYABgAAD...
iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB...
```

#### 支持的图片类型
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- BMP (.bmp)
- WebP (.webp)

### 使用示例

#### JavaScript/前端示例
```javascript
// 将文件转换为Base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}

// 上传图片进行评分
async function uploadImageForScoring(file, configId) {
    try {
        // 转换为Base64
        const base64Data = await fileToBase64(file);
        
        // 发送请求
        const response = await fetch('/api/v1/scores/calculate-base64', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            },
            body: JSON.stringify({
                image_data: base64Data,
                config_id: configId
            })
        });
        
        const result = await response.json();
        console.log('评分结果:', result);
        
    } catch (error) {
        console.error('上传失败:', error);
    }
}
```

#### Python示例
```python
import base64
import requests

def upload_image_base64(image_path, config_id, token):
    """使用Base64方式上传图片"""
    
    # 读取图片文件
    with open(image_path, 'rb') as f:
        image_data = f.read()
    
    # 转换为Base64
    base64_data = base64.b64encode(image_data).decode('utf-8')
    
    # 构造data URL
    image_type = image_path.split('.')[-1].lower()
    if image_type == 'jpg':
        image_type = 'jpeg'
    
    data_url = f"data:image/{image_type};base64,{base64_data}"
    
    # 发送请求
    response = requests.post(
        'http://localhost:8000/api/v1/scores/calculate-base64',
        headers={
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        },
        json={
            'image_data': data_url,
            'config_id': config_id
        }
    )
    
    return response.json()

# 使用示例
result = upload_image_base64('screenshot.jpg', 43, 'your_token_here')
print(result)
```

#### cURL示例
```bash
curl -X POST "http://localhost:8000/api/v1/scores/calculate-base64" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "image_data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
    "config_id": 43
  }'
```

### 响应格式

成功响应与文件上传API完全相同：
```json
{
  "total_score": 45.23,
  "calculation_context": {
    "config_name": "布兰特-通用",
    "config_id": 43,
    "situation": "3",
    "max_unaligned_score": 74.03,
    "aligned_score": 50
  },
  "breakdown": [...],
  "ocr_result": {...},
  "valid_terms_count": 6,
  "invalid_terms": [],
  "validation_errors": [],
  "main_props_found": [...],
  "sub_props_found": [...],
  "auto_detected_situation": "3"
}
```

### 错误处理

#### 常见错误类型

1. **无效的Base64格式**
```json
{
  "detail": "Base64解码失败: Invalid base64-encoded string"
}
```

2. **不支持的图片类型**
```json
{
  "detail": "不支持的图片类型: tiff，支持的类型: jpeg, jpg, png, gif, bmp, webp"
}
```

3. **无效的data URL格式**
```json
{
  "detail": "无效的data URL格式，应为: data:image/type;base64,..."
}
```

4. **非图片数据**
```json
{
  "detail": "解码后的数据不是有效的图片格式"
}
```

5. **参数验证错误**
```json
{
  "detail": "必须提供config_id或character_name其中一个参数"
}
```

## 技术实现

### 核心功能

#### 1. Base64解码
- 支持data URL格式解析
- 支持纯Base64数据
- 自动移除空白字符
- 严格的格式验证

#### 2. 图片格式验证
- 检查文件头签名
- 支持多种图片格式
- 防止非图片数据上传

#### 3. 错误处理
- 详细的错误信息
- 多层验证机制
- 用户友好的错误提示

### 安全特性

1. **格式验证**：严格验证图片格式，防止恶意文件上传
2. **大小限制**：通过Base64长度间接限制图片大小
3. **类型检查**：只允许支持的图片类型
4. **数据清理**：自动移除Base64数据中的空白字符

## 使用场景

### 适用场景
- **Web应用**：前端JavaScript直接处理图片
- **移动应用**：避免文件上传的复杂性
- **API集成**：纯JSON通信，无需处理multipart
- **批量处理**：可以在单个JSON请求中包含多个图片

### 性能考虑
- **优点**：无需处理文件上传，纯JSON通信
- **缺点**：Base64编码会增加约33%的数据大小
- **建议**：小图片使用Base64，大图片使用文件上传

## 总结

Base64图片上传功能为系统提供了更灵活的图片处理方式：

### 主要优势
- ✅ **简化集成**：纯JSON API，无需处理文件上传
- ✅ **前端友好**：JavaScript可直接处理
- ✅ **格式灵活**：支持data URL和纯Base64
- ✅ **类型丰富**：支持多种图片格式
- ✅ **安全可靠**：严格的验证和错误处理

### 使用建议
- 小于1MB的图片推荐使用Base64上传
- 大图片仍建议使用传统文件上传
- 前端应用优先考虑Base64方式
- 服务端集成可根据需求选择

这个功能完全兼容现有的评分系统，提供了更多样化的图片上传选择。
