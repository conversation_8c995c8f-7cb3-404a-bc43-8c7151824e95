# 映射逻辑修改报告

## 需求背景

用户要求修改未对齐最高分的映射顺序，从原来的顺序改为按照1、3、4的固定顺序进行映射，但不改变权重配置文件本身。

## 修改方案

### 核心思路
- **不修改配置文件**：保持所有角色配置文件（`calc.json`）不变
- **仅修改映射逻辑**：在系统内部重新排序`situation_map`和`score_max`
- **固定映射顺序**：始终按照1、3、4的顺序进行索引分配

### 技术实现

#### 修改位置
文件：`app/services/character_service.py`
方法：`_convert_to_api_format`

#### 原始逻辑
```python
# 原始逻辑：按照main_props中键的出现顺序分配索引
situations = list(main_props.keys())
situation_map = {situation: index for index, situation in enumerate(situations)}
```

#### 修改后逻辑
```python
# 新逻辑：按照固定顺序1、3、4分配索引
preferred_order = ["1", "3", "4"]
situation_map = {}

# 按照优先顺序分配索引
index = 0
for preferred_situation in preferred_order:
    if preferred_situation in situations:
        situation_map[preferred_situation] = index
        index += 1

# 处理其他情境（如果有的话）
for situation in situations:
    if situation not in situation_map:
        situation_map[situation] = index
        index += 1
```

#### score_max重新排序
```python
# 重新排序score_max数组以匹配新的映射顺序
original_score_max = config_data["score_max"]
original_situations = list(main_props.keys())

# 创建原始情境到分数的映射
original_situation_to_score = {}
for i, situation in enumerate(original_situations):
    if i < len(original_score_max):
        original_situation_to_score[situation] = original_score_max[i]

# 按照新的映射顺序重新排列score_max
score_max = []
for preferred_situation in preferred_order:
    if preferred_situation in original_situation_to_score:
        score_max.append(original_situation_to_score[preferred_situation])

# 添加其他情境的分数（如果有的话）
for situation in original_situations:
    if situation not in preferred_order and situation in original_situation_to_score:
        score_max.append(original_situation_to_score[situation])
```

## 测试验证

### 测试结果
✅ **所有测试用例通过**

#### 测试用例1：标准顺序配置
- **原始顺序**：['4', '3', '1']
- **原始score_max**：[84.059, 80.059, 76.509]
- **新映射**：{'1': 0, '3': 1, '4': 2}
- **新score_max**：[76.509, 80.059, 84.059]
- **结果**：✅ 映射顺序正确

#### 测试用例2：不同顺序配置
- **原始顺序**：['1', '3', '4']
- **原始score_max**：[76.509, 80.059, 84.059]
- **新映射**：{'1': 0, '3': 1, '4': 2}
- **新score_max**：[76.509, 80.059, 84.059]
- **结果**：✅ 映射顺序正确

#### 测试用例3：缺少情境配置
- **原始顺序**：['4', '3']
- **原始score_max**：[84.059, 80.059]
- **新映射**：{'3': 0, '4': 1}
- **新score_max**：[80.059, 84.059]
- **结果**：✅ 映射顺序正确

#### 真实配置测试：弗洛洛
- **原始顺序**：['4', '3', '1']
- **原始score_max**：[76.509, 80.059, 84.059]
- **新映射**：{'1': 0, '3': 1, '4': 2}
- **新score_max**：[84.059, 80.059, 76.509]
- **验证结果**：
  - ✅ COST1声骸 → 索引0 → 最高分84.059
  - ✅ COST3声骸 → 索引1 → 最高分80.059
  - ✅ COST4声骸 → 索引2 → 最高分76.509

## 映射效果

### 新的固定映射规则
无论配置文件中`main_props`的键顺序如何，系统都会按照以下固定顺序进行映射：

| 情境 | 索引 | 说明 |
|------|------|------|
| "1" | 0 | COST1声骸，优先级最高 |
| "3" | 1 | COST3声骸，优先级中等 |
| "4" | 2 | COST4声骸，优先级最低 |

### 实际效果示例

#### 修改前（按配置文件键顺序）
```json
// 配置文件中的顺序：4, 3, 1
"situation_map": {"4": 0, "3": 1, "1": 2}
"score_max": [84.059, 80.059, 76.509]
// 结果：4→84.059, 3→80.059, 1→76.509
```

#### 修改后（固定1、3、4顺序）
```json
// 系统内部重新排序：1, 3, 4
"situation_map": {"1": 0, "3": 1, "4": 2}
"score_max": [76.509, 80.059, 84.059]
// 结果：1→76.509, 3→80.059, 4→84.059
```

## 优势特点

### 1. 配置文件不变
- ✅ 所有角色配置文件保持原样
- ✅ 不需要手动修改任何JSON文件
- ✅ 保持配置文件的原始结构和可读性

### 2. 逻辑统一
- ✅ 所有角色都使用相同的映射顺序
- ✅ 消除了配置文件键顺序的影响
- ✅ 提供了一致的用户体验

### 3. 向后兼容
- ✅ 不影响现有功能
- ✅ 支持缺少某些情境的配置
- ✅ 自动处理异常情况

### 4. 易于维护
- ✅ 集中的映射逻辑
- ✅ 清晰的代码结构
- ✅ 便于后续调整

## 使用说明

### 生效方式
修改后需要重新导入角色配置到数据库：

#### 方法1：重新初始化数据库
```bash
python init_db.py
```

#### 方法2：使用API重新导入
```bash
# 重新导入所有角色配置
curl -X POST "http://localhost:8000/api/v1/characters/import" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"force_update": true}'
```

### 验证效果
可以通过以下方式验证修改效果：

#### 1. 查看配置API
```bash
curl -X GET "http://localhost:8000/api/v1/configs/{config_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 2. 检查situation_map
返回的配置中，`situation_map`应该按照1、3、4的顺序：
```json
{
  "situation_map": {"1": 0, "3": 1, "4": 2},
  "score_max": [76.509, 80.059, 84.059]
}
```

## 影响范围

### 受影响功能
- ✅ **评分计算**：使用新的映射顺序计算未对齐最高分
- ✅ **配置导入**：角色配置导入时应用新的映射逻辑
- ✅ **API响应**：配置查询API返回新的映射结构

### 不受影响功能
- ✅ **配置文件**：所有JSON配置文件保持不变
- ✅ **评分算法**：核心评分计算逻辑不变
- ✅ **用户接口**：API接口和参数不变

## 技术细节

### 关键代码位置
- **文件**：`app/services/character_service.py`
- **方法**：`_convert_to_api_format`
- **行数**：211-257

### 核心改进点
1. **固定顺序映射**：不再依赖配置文件中的键顺序
2. **智能重排序**：自动重新排列score_max数组
3. **异常处理**：处理缺少情境的情况
4. **向后兼容**：支持各种配置文件格式

## 总结

通过修改映射逻辑，成功实现了：

### 核心目标
- ✅ **固定映射顺序**：未对齐最高分按照1、3、4顺序映射
- ✅ **配置文件不变**：不需要修改任何角色配置文件
- ✅ **逻辑统一**：所有角色使用相同的映射规则

### 技术优势
- 🎯 **简洁高效**：仅修改一个方法即可实现
- 🎯 **向后兼容**：不影响现有功能和数据
- 🎯 **易于维护**：集中的映射逻辑便于管理
- 🎯 **测试完备**：全面的测试验证确保正确性

现在系统的未对齐最高分将始终按照1、3、4的顺序进行映射，提供了更加一致和可预测的评分体验！
