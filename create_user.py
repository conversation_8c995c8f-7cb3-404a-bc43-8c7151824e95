#!/usr/bin/env python3
"""
用户创建脚本
用于快速创建新用户的命令行工具
"""

import argparse
import logging
import sys
from getpass import getpass
from sqlalchemy.orm import Session

from app.core.security import get_password_hash
from app.db.session import SessionLocal
from app.models.user import User

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_user(
    username: str,
    password: str,
    email: str = None,
    role: str = "user",
    is_active: bool = True
) -> bool:
    """
    创建新用户
    
    Args:
        username: 用户名
        password: 密码
        email: 邮箱（可选）
        role: 角色（admin或user）
        is_active: 是否激活
        
    Returns:
        创建是否成功
    """
    db = SessionLocal()
    try:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == username).first()
        if existing_user:
            logger.error(f"用户名 '{username}' 已存在")
            return False
        
        # 检查邮箱是否已存在（如果提供了邮箱）
        if email:
            existing_email = db.query(User).filter(User.email == email).first()
            if existing_email:
                logger.error(f"邮箱 '{email}' 已存在")
                return False
        
        # 创建用户
        new_user = User(
            username=username,
            email=email,
            hashed_password=get_password_hash(password),
            role=role,
            is_active=is_active
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        logger.info(f"用户 '{username}' 创建成功")
        logger.info(f"  ID: {new_user.id}")
        logger.info(f"  用户名: {new_user.username}")
        logger.info(f"  邮箱: {new_user.email or '未设置'}")
        logger.info(f"  角色: {new_user.role}")
        logger.info(f"  状态: {'激活' if new_user.is_active else '未激活'}")
        logger.info(f"  创建时间: {new_user.created_at}")
        
        return True
        
    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="创建新用户")
    parser.add_argument("username", help="用户名")
    parser.add_argument("-e", "--email", help="邮箱地址")
    parser.add_argument("-r", "--role", choices=["admin", "user"], default="user", help="用户角色")
    parser.add_argument("--inactive", action="store_true", help="创建未激活用户")
    parser.add_argument("-p", "--password", help="密码（不推荐在命令行中使用）")
    
    args = parser.parse_args()
    
    # 获取密码
    if args.password:
        password = args.password
        logger.warning("在命令行中直接提供密码不安全，建议使用交互式输入")
    else:
        password = getpass("请输入密码: ")
        confirm_password = getpass("请确认密码: ")
        
        if password != confirm_password:
            logger.error("两次输入的密码不一致")
            sys.exit(1)
    
    if len(password) < 6:
        logger.error("密码长度至少为6位")
        sys.exit(1)
    
    # 创建用户
    success = create_user(
        username=args.username,
        password=password,
        email=args.email,
        role=args.role,
        is_active=not args.inactive
    )
    
    if success:
        print("\n用户创建成功！")
        if args.role == "admin":
            print("该用户具有管理员权限，可以:")
            print("- 管理其他用户")
            print("- 管理权重配置")
            print("- 访问所有API端点")
        else:
            print("该用户为普通用户，可以:")
            print("- 使用评分功能")
            print("- 查看角色信息")
        sys.exit(0)
    else:
        print("\n用户创建失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
