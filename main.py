"""
Smart Scoring API - 主应用入口文件
基于OCR的智能图片评分系统后端服务
"""

import logging
import time
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from app.core.config import settings
from app.core.logging_config import setup_logging, AccessLogMiddleware
from app.api.v1.router import api_router
from app.db.session import init_database, get_database_status, get_engine
from app.db.base import Base

# 设置日志系统
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 正在启动 Smart Scoring API...")

    # 初始化数据库连接
    db_success = init_database()

    # 获取数据库状态
    db_status = get_database_status()

    if db_success and db_status['available']:
        # 创建数据库表（如果不存在）
        try:
            # 获取已初始化的数据库引擎
            engine = get_engine()
            if engine is not None:
                Base.metadata.create_all(bind=engine)
                logger.info("✅ 数据库表创建/检查完成")
                logger.info("✅ 数据库功能已启用")
            else:
                logger.error("❌ 数据库引擎未正确初始化")
                logger.warning("⚠️ 数据库功能可能不完整")
        except Exception as e:
            logger.error(f"❌ 数据库表创建失败: {e}")
            logger.warning("⚠️ 数据库功能可能不完整")
    else:
        logger.warning("⚠️ 应用将在无数据库模式下运行")
        logger.warning("💡 某些功能（如用户管理、评分历史）将不可用")
        logger.info("✅ OCR识别和评分计算功能仍然可用")

    # 显示数据库状态
    logger.info(f"📊 数据库状态: {db_status}")

    logger.info("🎉 Smart Scoring API 启动完成")

    yield

    # 关闭时执行
    logger.info("🔄 正在关闭 Smart Scoring API...")
    logger.info("✅ Smart Scoring API 已关闭")

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="智能评分API系统 - 基于OCR的图片评分后端服务",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan
)

# 配置CORS中间件
if settings.CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins_list,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 配置可信主机中间件（生产环境安全）
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["localhost", "127.0.0.1"]
)

# 添加访问日志中间件
app.add_middleware(AccessLogMiddleware)


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"❌ 未处理的异常: {request.method} {request.url} - {str(exc)}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "error": "InternalServerError",
            "message": "服务器内部错误，请稍后重试",
            "details": str(exc) if settings.DEBUG else None
        }
    )

# 挂载API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "Smart Scoring API is running!",
        "version": settings.VERSION,
        "docs_url": f"{settings.API_V1_STR}/docs"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "smart-scoring-api"}


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug"
    )
