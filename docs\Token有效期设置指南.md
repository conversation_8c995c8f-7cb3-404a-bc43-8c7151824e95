# Token有效期设置指南

## 概述

智能评分API系统使用JWT（JSON Web Token）进行用户认证。Token有效期可以通过多种方式进行配置，以满足不同的安全需求和使用场景。

## 当前配置

### 默认设置
- **当前有效期**: 30分钟
- **配置位置**: `.env` 文件中的 `ACCESS_TOKEN_EXPIRE_MINUTES=30`
- **配置方式**: 环境变量

## 设置Token有效期的方法

### 1. 通过环境变量设置（推荐）

#### 修改 .env 文件
```bash
# JWT配置
SECRET_KEY=9965421
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60  # 设置为60分钟
```

#### 常用有效期设置
```bash
# 短期会话（适合高安全要求）
ACCESS_TOKEN_EXPIRE_MINUTES=15    # 15分钟

# 标准会话（默认设置）
ACCESS_TOKEN_EXPIRE_MINUTES=30    # 30分钟

# 长期会话（适合开发测试）
ACCESS_TOKEN_EXPIRE_MINUTES=60    # 1小时
ACCESS_TOKEN_EXPIRE_MINUTES=120   # 2小时
ACCESS_TOKEN_EXPIRE_MINUTES=480   # 8小时
ACCESS_TOKEN_EXPIRE_MINUTES=1440  # 24小时

# 超长期会话（不推荐生产环境）
ACCESS_TOKEN_EXPIRE_MINUTES=10080 # 7天
```

### 2. 通过Docker环境变量设置

#### docker-compose.yml 配置
```yaml
services:
  api:
    environment:
      - ACCESS_TOKEN_EXPIRE_MINUTES=60  # 设置为60分钟
```

#### Docker运行时设置
```bash
docker run -e ACCESS_TOKEN_EXPIRE_MINUTES=60 smartscoring-api
```

### 3. 通过代码动态设置

#### 在登录时指定不同有效期
```python
# app/api/v1/endpoints/auth.py

from datetime import timedelta

@router.post("/login-extended", response_model=Token)
def login_with_extended_token(
    login_data: LoginRequest,
    db: Session = Depends(deps.get_db)
) -> Any:
    """登录获取长期有效令牌（24小时）"""
    
    # 验证用户...
    authenticated_user = user.authenticate(db, username=login_data.username, password=login_data.password)
    
    # 创建24小时有效期的令牌
    access_token_expires = timedelta(hours=24)  # 24小时
    access_token = create_access_token(
        subject=authenticated_user.username, 
        expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}
```

## 不同场景的推荐设置

### 1. 生产环境
```bash
# 高安全要求
ACCESS_TOKEN_EXPIRE_MINUTES=15    # 15分钟

# 标准安全要求
ACCESS_TOKEN_EXPIRE_MINUTES=30    # 30分钟

# 用户友好设置
ACCESS_TOKEN_EXPIRE_MINUTES=60    # 1小时
```

### 2. 开发环境
```bash
# 开发便利性
ACCESS_TOKEN_EXPIRE_MINUTES=480   # 8小时

# 测试环境
ACCESS_TOKEN_EXPIRE_MINUTES=120   # 2小时
```

### 3. API集成
```bash
# 服务间调用
ACCESS_TOKEN_EXPIRE_MINUTES=60    # 1小时

# 批量处理
ACCESS_TOKEN_EXPIRE_MINUTES=240   # 4小时
```

## 实时修改Token有效期

### 1. 修改环境变量后重启服务
```bash
# 1. 修改 .env 文件
echo "ACCESS_TOKEN_EXPIRE_MINUTES=60" >> .env

# 2. 重启服务
# 如果使用Docker
docker-compose restart api

# 如果直接运行
# 停止服务后重新启动
python main.py
```

### 2. 不重启服务的动态修改（高级）

可以添加一个管理员API来动态修改设置：

```python
# 新增API端点（仅供参考）
@router.post("/admin/update-token-expire")
def update_token_expire(
    minutes: int,
    current_user: User = Depends(deps.get_current_admin_user)
):
    """动态更新Token有效期（管理员专用）"""
    if minutes < 5 or minutes > 10080:  # 5分钟到7天
        raise HTTPException(400, "有效期必须在5分钟到7天之间")
    
    # 更新配置（需要实现配置管理）
    settings.ACCESS_TOKEN_EXPIRE_MINUTES = minutes
    
    return {"message": f"Token有效期已更新为{minutes}分钟"}
```

## 验证Token有效期

### 1. 检查当前设置
```bash
# 查看环境变量
echo $ACCESS_TOKEN_EXPIRE_MINUTES

# 或查看.env文件
grep ACCESS_TOKEN_EXPIRE_MINUTES .env
```

### 2. 测试Token过期
```python
import requests
import time

# 1. 登录获取token
response = requests.post("http://localhost:8000/api/v1/auth/login-json", 
                        json={"username": "admin", "password": "admin123"})
token = response.json()["access_token"]

# 2. 立即测试token
test_response = requests.post("http://localhost:8000/api/v1/auth/test-token",
                             headers={"Authorization": f"Bearer {token}"})
print("立即测试:", test_response.status_code)  # 应该是200

# 3. 等待token过期后测试（根据设置的有效期）
# time.sleep(1800)  # 等待30分钟（如果设置为30分钟）
# expired_response = requests.post("http://localhost:8000/api/v1/auth/test-token",
#                                  headers={"Authorization": f"Bearer {token}"})
# print("过期测试:", expired_response.status_code)  # 应该是401
```

## 安全考虑

### 1. 有效期长短的权衡

| 有效期 | 优点 | 缺点 | 适用场景 |
|--------|------|------|----------|
| 短期(5-15分钟) | 高安全性 | 用户体验差，需频繁登录 | 高敏感操作 |
| 中期(30-60分钟) | 安全性与便利性平衡 | 需要定期重新认证 | 一般业务操作 |
| 长期(2-24小时) | 用户体验好 | 安全风险较高 | 开发测试环境 |

### 2. 安全建议

1. **生产环境**：建议使用30-60分钟
2. **敏感操作**：考虑实现操作级别的重新认证
3. **移动应用**：可以使用refresh token机制
4. **API集成**：根据调用频率调整有效期

### 3. 监控和日志

```python
# 添加token过期监控
import logging

logger = logging.getLogger(__name__)

def verify_token_with_logging(token: str) -> Optional[str]:
    """带日志的token验证"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username = payload.get("sub")
        exp = payload.get("exp")
        
        # 记录token使用情况
        logger.info(f"Token验证成功: 用户={username}, 过期时间={exp}")
        
        return username
    except jwt.ExpiredSignatureError:
        logger.warning("Token已过期")
        return None
    except JWTError as e:
        logger.error(f"Token验证失败: {e}")
        return None
```

## 常见问题

### Q1: 修改有效期后需要重启服务吗？
**A**: 是的，修改环境变量后需要重启服务才能生效。

### Q2: 已经发出的token会受到新设置影响吗？
**A**: 不会，已发出的token仍按原有效期执行，新设置只影响新生成的token。

### Q3: 如何实现"记住我"功能？
**A**: 可以创建两个登录端点，一个标准有效期，一个长期有效期。

### Q4: 如何强制所有token失效？
**A**: 更改SECRET_KEY会使所有现有token失效，但需要重启服务。

## 总结

Token有效期设置是安全性和用户体验之间的平衡。根据你的具体需求：

- **快速修改**：直接编辑`.env`文件中的`ACCESS_TOKEN_EXPIRE_MINUTES`值
- **推荐设置**：生产环境30-60分钟，开发环境2-8小时
- **安全原则**：宁可短一些，配合良好的用户体验设计

修改后记得重启服务使配置生效！
