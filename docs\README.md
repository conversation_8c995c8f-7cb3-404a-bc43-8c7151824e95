# Smart Scoring API 文档中心

欢迎来到 Smart Scoring API 的文档中心！这里包含了项目的完整文档。

## 📚 文档目录

### 🚀 快速开始
- [项目 README](../README.md) - 项目概述和快速开始指南
- [API 使用指南](api-guide.md) - 详细的 API 使用说明和示例

### 🔧 部署和运维
- [部署指南](deployment.md) - 完整的部署说明，包括生产环境配置
- [Docker 配置](../docker-compose.yml) - 容器化部署配置

### 📖 项目文档
- [项目总结](project-summary.md) - 项目的完整技术总结和架构说明

### 🧪 测试和开发
- [测试脚本](../test_api_manual.py) - 手动 API 测试脚本
- [单元测试](../tests/) - 自动化测试代码

## 🔗 在线文档

当服务运行时，您可以访问以下在线文档：

- **Swagger UI**: http://localhost:8000/api/v1/docs
- **ReDoc**: http://localhost:8000/api/v1/redoc

## 📋 文档使用指南

### 新用户
1. 先阅读 [项目 README](../README.md) 了解项目概况
2. 按照 [部署指南](deployment.md) 部署服务
3. 参考 [API 使用指南](api-guide.md) 开始使用 API

### 开发者
1. 阅读 [项目总结](project-summary.md) 了解技术架构
2. 查看代码结构和注释
3. 运行测试确保环境正常

### 运维人员
1. 重点关注 [部署指南](deployment.md) 中的生产环境配置
2. 了解监控和维护相关内容
3. 熟悉故障排除流程

## 🆘 获取帮助

如果您在使用过程中遇到问题：

1. 查看相关文档是否有解决方案
2. 检查 [GitHub Issues](https://github.com/your-org/smart-scoring-api/issues)
3. 联系技术支持: <EMAIL>

## 📝 文档贡献

我们欢迎您为文档做出贡献：

1. 发现错误或不清楚的地方，请提交 Issue
2. 有改进建议，请提交 Pull Request
3. 分享使用经验和最佳实践

## 📄 许可证

本文档遵循项目的 MIT 许可证。
