"""
日志管理API端点
提供日志查看、统计和管理功能
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import FileResponse

from app.api import deps
from app.core.config import settings
from app.core.logging_config import logging_config
from app.models.user import User

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/stats", response_model=Dict[str, Any])
async def get_log_stats(
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取日志统计信息
    
    需要管理员权限
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    try:
        stats = logging_config.get_log_stats()
        logger.info(f"📊 管理员 {current_user.username} 查看日志统计")
        return stats
    except Exception as e:
        logger.error(f"❌ 获取日志统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取日志统计失败"
        )


@router.get("/files", response_model=List[Dict[str, Any]])
async def list_log_files(
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    列出所有日志文件
    
    需要管理员权限
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    try:
        logs_dir = Path("logs")
        if not logs_dir.exists():
            return []
        
        log_files = []
        for log_file in logs_dir.glob("*.log*"):
            if log_file.is_file():
                file_stat = log_file.stat()
                log_files.append({
                    "name": log_file.name,
                    "size_bytes": file_stat.st_size,
                    "size_mb": round(file_stat.st_size / 1024 / 1024, 2),
                    "modified": file_stat.st_mtime,
                    "path": str(log_file)
                })
        
        # 按修改时间排序
        log_files.sort(key=lambda x: x["modified"], reverse=True)
        
        logger.info(f"📁 管理员 {current_user.username} 查看日志文件列表")
        return log_files
        
    except Exception as e:
        logger.error(f"❌ 列出日志文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="列出日志文件失败"
        )


@router.get("/download/{filename}")
async def download_log_file(
    filename: str,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    下载日志文件
    
    需要管理员权限
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    try:
        logs_dir = Path("logs")
        log_file = logs_dir / filename
        
        # 安全检查：确保文件在logs目录内
        if not log_file.resolve().is_relative_to(logs_dir.resolve()):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的文件路径"
            )
        
        if not log_file.exists() or not log_file.is_file():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日志文件不存在"
            )
        
        logger.info(f"📥 管理员 {current_user.username} 下载日志文件: {filename}")
        
        return FileResponse(
            path=str(log_file),
            filename=filename,
            media_type="text/plain; charset=utf-8"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 下载日志文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="下载日志文件失败"
        )


@router.get("/tail/{filename}")
async def tail_log_file(
    filename: str,
    lines: int = Query(default=100, ge=1, le=1000, description="读取的行数"),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取日志文件的最后N行
    
    需要管理员权限
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    try:
        logs_dir = Path("logs")
        log_file = logs_dir / filename
        
        # 安全检查
        if not log_file.resolve().is_relative_to(logs_dir.resolve()):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的文件路径"
            )
        
        if not log_file.exists() or not log_file.is_file():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日志文件不存在"
            )
        
        # 读取文件最后N行
        with open(log_file, 'r', encoding='utf-8') as f:
            file_lines = f.readlines()
            tail_lines = file_lines[-lines:] if len(file_lines) > lines else file_lines
        
        logger.info(f"👀 管理员 {current_user.username} 查看日志文件尾部: {filename} ({lines}行)")
        
        return {
            "filename": filename,
            "total_lines": len(file_lines),
            "returned_lines": len(tail_lines),
            "lines": [line.rstrip('\n') for line in tail_lines]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 读取日志文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="读取日志文件失败"
        )


@router.delete("/cleanup")
async def cleanup_old_logs(
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    手动清理过期日志文件
    
    需要管理员权限
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    try:
        # 执行清理
        logging_config._cleanup_old_logs()
        
        logger.info(f"🧹 管理员 {current_user.username} 手动清理过期日志")
        
        return {
            "message": "日志清理完成",
            "retention_days": settings.LOG_FILE_RETENTION_DAYS
        }
        
    except Exception as e:
        logger.error(f"❌ 清理日志失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清理日志失败"
        )


@router.get("/search/{filename}")
async def search_log_file(
    filename: str,
    query: str = Query(..., min_length=1, description="搜索关键词"),
    max_results: int = Query(default=100, ge=1, le=500, description="最大结果数"),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    在日志文件中搜索关键词
    
    需要管理员权限
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    try:
        logs_dir = Path("logs")
        log_file = logs_dir / filename
        
        # 安全检查
        if not log_file.resolve().is_relative_to(logs_dir.resolve()):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的文件路径"
            )
        
        if not log_file.exists() or not log_file.is_file():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日志文件不存在"
            )
        
        # 搜索关键词
        matches = []
        with open(log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if query.lower() in line.lower():
                    matches.append({
                        "line_number": line_num,
                        "content": line.rstrip('\n')
                    })
                    
                    if len(matches) >= max_results:
                        break
        
        logger.info(f"🔍 管理员 {current_user.username} 搜索日志: {filename} - '{query}' ({len(matches)}个结果)")
        
        return {
            "filename": filename,
            "query": query,
            "total_matches": len(matches),
            "max_results": max_results,
            "matches": matches
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 搜索日志失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="搜索日志失败"
        )
