"""
角色配置API端点
处理角色配置的自动导入和管理
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services import character_service
from app.crud import weight_config

router = APIRouter()


@router.get("/list")
def get_character_list(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取所有可用角色列表，包含config_id信息

    Args:
        db: 数据库会话
        current_user: 当前认证用户

    Returns:
        角色信息列表，包含角色名和对应的config_id
    """
    try:
        characters = character_service.get_character_list_with_config_ids(db)
        return characters
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色列表失败: {str(e)}"
        )


@router.get("/aliases")
def get_character_aliases(
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取角色别名配置

    Args:
        current_user: 当前认证用户

    Returns:
        角色别名配置信息
    """
    try:
        aliases_map = character_service.load_aliases()

        # 重新组织数据，按角色分组
        aliases_by_character = {}
        for alias, character_name in aliases_map.items():
            if character_name not in aliases_by_character:
                aliases_by_character[character_name] = []
            aliases_by_character[character_name].append(alias)

        return {
            "aliases_by_character": aliases_by_character,
            "total_aliases": len(aliases_map),
            "total_characters": len(aliases_by_character),
            "usage": "可以使用任何别名来代替角色名进行评分计算"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色别名配置失败: {str(e)}"
        )


@router.get("/{character_name}/config")
def get_character_config(
    *,
    character_name: str,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取指定角色的原始配置
    
    Args:
        character_name: 角色名称
        current_user: 当前认证用户
        
    Returns:
        角色配置数据
        
    Raises:
        HTTPException: 角色不存在时抛出404错误
    """
    try:
        config_data = character_service.get_character_config(character_name)
        if not config_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色配置不存在: {character_name}"
            )
        
        # 添加角色名称到响应中
        config_data["character_name"] = character_name
        return config_data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色配置失败: {str(e)}"
        )


@router.post("/import", status_code=status.HTTP_201_CREATED)
def import_character_configs(
    *,
    db: Session = Depends(deps.get_db),
    force_update: bool = Query(False, description="是否强制更新已存在的配置"),
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    导入所有角色配置到数据库
    
    Args:
        db: 数据库会话
        force_update: 是否强制更新已存在的配置
        current_user: 当前管理员用户
        
    Returns:
        导入结果统计
    """
    try:
        imported_count, skipped_count = character_service.import_to_database(
            db, force_update=force_update
        )
        
        return {
            "message": "角色配置导入完成",
            "imported_count": imported_count,
            "skipped_count": skipped_count,
            "total_processed": imported_count + skipped_count,
            "force_update": force_update
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导入角色配置失败: {str(e)}"
        )


@router.post("/{character_name}/import", status_code=status.HTTP_201_CREATED)
def import_single_character_config(
    *,
    db: Session = Depends(deps.get_db),
    character_name: str,
    force_update: bool = Query(False, description="是否强制更新已存在的配置"),
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    导入单个角色配置到数据库
    
    Args:
        db: 数据库会话
        character_name: 角色名称
        force_update: 是否强制更新已存在的配置
        current_user: 当前管理员用户
        
    Returns:
        导入结果
        
    Raises:
        HTTPException: 角色不存在时抛出404错误
    """
    try:
        # 获取角色配置
        config_data = character_service.get_character_config(character_name)
        if not config_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色配置不存在: {character_name}"
            )
        
        # 转换为API格式
        config_data["character_name"] = character_name
        api_config = character_service.convert_to_api_format(config_data)
        
        # 检查是否已存在
        existing_config = weight_config.get_by_name(db, name=api_config["name"])
        
        if existing_config:
            if force_update:
                # 更新现有配置
                updated_config = weight_config.update(
                    db, 
                    db_obj=existing_config, 
                    obj_in=api_config
                )
                return {
                    "message": f"角色配置更新成功: {character_name}",
                    "config_id": updated_config.id,
                    "config_name": updated_config.name,
                    "action": "updated"
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"配置已存在: {api_config['name']}，使用 force_update=true 强制更新"
                )
        else:
            # 创建新配置
            from app.schemas.weight_config import WeightConfigCreate
            config_create = WeightConfigCreate(**api_config)
            new_config = weight_config.create(db, obj_in=config_create)
            
            return {
                "message": f"角色配置导入成功: {character_name}",
                "config_id": new_config.id,
                "config_name": new_config.name,
                "action": "created"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导入角色配置失败: {str(e)}"
        )


@router.get("/scan")
def scan_character_configs(
    current_user: User = Depends(deps.get_current_admin_user)
) -> Any:
    """
    扫描所有角色配置文件（不导入数据库）
    
    Args:
        current_user: 当前管理员用户
        
    Returns:
        扫描结果
    """
    try:
        configs = character_service.scan_character_configs()
        
        return {
            "message": "角色配置扫描完成",
            "total_configs": len(configs),
            "characters": [
                {
                    "character_name": config.get("character_name"),
                    "config_name": config.get("name"),
                    "situations": list(config.get("main_props", {}).keys()),
                    "situation_count": len(config.get("main_props", {}))
                }
                for config in configs
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"扫描角色配置失败: {str(e)}"
        )


@router.get("/{character_name}/situations")
def get_character_situations(
    *,
    character_name: str,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取指定角色支持的情境列表
    
    Args:
        character_name: 角色名称
        current_user: 当前认证用户
        
    Returns:
        情境列表
        
    Raises:
        HTTPException: 角色不存在时抛出404错误
    """
    try:
        config_data = character_service.get_character_config(character_name)
        if not config_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色配置不存在: {character_name}"
            )
        
        situations = list(config_data.get("main_props", {}).keys())
        
        return {
            "character_name": character_name,
            "situations": situations,
            "situation_count": len(situations)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色情境失败: {str(e)}"
        )
