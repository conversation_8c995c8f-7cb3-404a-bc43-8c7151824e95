"""
权重配置相关的数据模式
定义复杂的权重配置数据结构和验证规则
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator


class WeightConfigBase(BaseModel):
    """权重配置基础模式"""
    
    name: str = Field(..., min_length=1, max_length=100, description="配置名称")
    description: Optional[str] = Field(None, max_length=500, description="配置描述")
    main_props: Dict[str, Dict[str, float]] = Field(..., description="主词条权重配置")
    sub_props: Dict[str, float] = Field(..., description="副词条权重配置")
    score_max: List[float] = Field(..., description="未对齐最高分列表")
    situation_map: Dict[str, int] = Field(..., description="情境映射")
    
    @validator('main_props')
    def validate_main_props(cls, v):
        """验证主词条权重配置"""
        if not v:
            raise ValueError("主词条权重配置不能为空")
        
        for situation, props in v.items():
            if not isinstance(situation, str) or not situation.strip():
                raise ValueError("情境标识符必须是非空字符串")
            if not isinstance(props, dict) or not props:
                raise ValueError(f"情境 {situation} 的权重配置必须是非空字典")
            for prop_name, weight in props.items():
                if not isinstance(prop_name, str) or not prop_name.strip():
                    raise ValueError("词条名称必须是非空字符串")
                if not isinstance(weight, (int, float)) or weight < 0:
                    raise ValueError(f"权重值必须是非负数，当前值: {weight}")
        return v
    
    @validator('sub_props')
    def validate_sub_props(cls, v):
        """验证副词条权重配置"""
        if not v:
            raise ValueError("副词条权重配置不能为空")
        
        for prop_name, weight in v.items():
            if not isinstance(prop_name, str) or not prop_name.strip():
                raise ValueError("词条名称必须是非空字符串")
            if not isinstance(weight, (int, float)) or weight < 0:
                raise ValueError(f"权重值必须是非负数，当前值: {weight}")
        return v
    
    @validator('score_max')
    def validate_score_max(cls, v):
        """验证未对齐最高分列表"""
        if not v:
            raise ValueError("未对齐最高分列表不能为空")
        
        for i, score in enumerate(v):
            if not isinstance(score, (int, float)) or score <= 0:
                raise ValueError(f"最高分必须是正数，索引 {i} 的值: {score}")
        return v
    
    @validator('situation_map')
    def validate_situation_map(cls, v, values):
        """验证情境映射"""
        if not v:
            raise ValueError("情境映射不能为空")
        
        # 检查映射的键是否与main_props中的情境一致
        if 'main_props' in values:
            main_situations = set(values['main_props'].keys())
            map_situations = set(v.keys())
            if main_situations != map_situations:
                raise ValueError(f"情境映射的键必须与主词条配置的情境一致。"
                               f"主词条情境: {main_situations}, 映射情境: {map_situations}")
        
        # 检查映射的值是否为有效索引
        if 'score_max' in values:
            max_index = len(values['score_max']) - 1
            for situation, index in v.items():
                if not isinstance(index, int) or index < 0 or index > max_index:
                    raise ValueError(f"情境 {situation} 的索引 {index} 超出范围 [0, {max_index}]")
        
        return v


class WeightConfigCreate(WeightConfigBase):
    """权重配置创建模式"""
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "某角色-配置A",
                "description": "适用于某角色的通用配置",
                "main_props": {
                    "c4": {"词条A": 0.5, "词条B": 0.25},
                    "c1": {"词条A": 0.4}
                },
                "sub_props": {
                    "词条C": 1.0,
                    "词条D": 0.3
                },
                "score_max": [75.0, 80.0],
                "situation_map": {"c4": 0, "c1": 1}
            }
        }


class WeightConfigUpdate(BaseModel):
    """权重配置更新模式"""
    
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="配置名称")
    description: Optional[str] = Field(None, max_length=500, description="配置描述")
    main_props: Optional[Dict[str, Dict[str, float]]] = Field(None, description="主词条权重配置")
    sub_props: Optional[Dict[str, float]] = Field(None, description="副词条权重配置")
    score_max: Optional[List[float]] = Field(None, description="未对齐最高分列表")
    situation_map: Optional[Dict[str, int]] = Field(None, description="情境映射")
    is_active: Optional[str] = Field(None, description="是否激活", pattern="^(active|inactive)$")


class WeightConfigResponse(WeightConfigBase):
    """权重配置响应模式"""
    
    id: int = Field(..., description="配置ID")
    is_active: str = Field(..., description="是否激活")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class WeightConfigListResponse(BaseModel):
    """权重配置列表响应模式"""
    
    configs: List[WeightConfigResponse] = Field(..., description="配置列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
