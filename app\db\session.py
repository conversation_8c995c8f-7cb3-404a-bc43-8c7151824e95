"""
数据库会话管理
创建数据库引擎和会话工厂
"""

import logging
from typing import Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import settings

logger = logging.getLogger(__name__)

# 全局变量
engine: Optional[object] = None
SessionLocal: Optional[sessionmaker] = None
database_available = False


def init_database():
    """初始化数据库连接"""
    global engine, SessionLocal, database_available

    if not settings.DATABASE_URL:
        logger.warning("⚠️ 未配置数据库连接 (DATABASE_URL)，数据库功能将不可用")
        logger.warning("💡 请在配置文件中设置 DATABASE_URL 以启用数据库功能")
        return False

    try:
        # 创建数据库引擎
        engine = create_engine(
            settings.DATABASE_URL,
            pool_pre_ping=True,  # 连接池预检查，确保连接有效
            pool_recycle=300,    # 连接回收时间（秒）
            echo=settings.DEBUG  # 在调试模式下打印SQL语句
        )

        # 测试数据库连接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))

        # 创建会话工厂
        global SessionLocal
        SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=engine
        )

        database_available = True
        logger.info("✅ 数据库连接成功")
        return True

    except SQLAlchemyError as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        logger.warning("⚠️ 数据库功能将不可用，请检查数据库配置和连接")
        return False
    except Exception as e:
        logger.error(f"❌ 数据库初始化异常: {e}")
        logger.warning("⚠️ 数据库功能将不可用")
        return False


def get_database_status() -> dict:
    """获取数据库状态信息"""
    return {
        "available": database_available,
        "configured": bool(settings.DATABASE_URL),
        "engine_created": engine is not None,
        "session_factory_created": SessionLocal is not None
    }


def get_engine():
    """获取数据库引擎"""
    return engine
