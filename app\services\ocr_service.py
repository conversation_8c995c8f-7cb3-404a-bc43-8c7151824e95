"""
OCR服务
封装PaddleOCR调用和文本解析逻辑
"""

import re
import time
import logging
from typing import Dict, List, Optional, Tuple
from io import BytesIO

import numpy as np
from PIL import Image
from paddleocr import PaddleOCR

from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)


class OCRService:
    """OCR服务类"""
    
    def __init__(self):
        """初始化OCR引擎"""
        self.ocr = PaddleOCR(
            use_angle_cls=True,
            lang=settings.OCR_LANG,
            use_gpu=settings.OCR_USE_GPU,
            show_log=False
        )
        
        # 标准词条库
        self.standard_terms = [
            "攻击", "攻击%", "生命", "生命%", "防御", "防御%",
            "暴击", "暴击伤害", "共鸣效率", "属性伤害加成",
            "普攻伤害加成", "重击伤害加成", "共鸣技能伤害加成", 
            "共鸣解放伤害加成", "治疗效果加成"
        ]
        
        # 繁体字到简体字映射
        self.traditional_to_simplified = {
            # 基础词条繁体字
            "攻擊": "攻击", "攻撃": "攻击", "攻擎": "攻击",  # 攻击的各种繁体/错误识别
            "生命": "生命",  # 生命本身就是简体
            "防禦": "防御", "防御": "防御", "防薬": "防御",  # 防御的各种形式
            "暴擊": "暴击", "暴撃": "暴击",  # 暴击的繁体形式
            "傷害": "伤害", "損害": "伤害",  # 伤害的繁体形式
            "效率": "效率",  # 效率本身就是简体
            "加成": "加成",  # 加成本身就是简体
            "治療": "治疗",  # 治疗的繁体形式
            "共鳴": "共鸣",  # 共鸣的繁体形式
            "屬性": "属性",  # 属性的繁体形式
            "普攻": "普攻",  # 普攻本身就是简体
            "重擊": "重击",  # 重击的繁体形式
            "技能": "技能",  # 技能本身就是简体
            "解放": "解放",  # 解放本身就是简体

            # 组合词条的繁体形式
            "攻擊力": "攻击力", "攻撃力": "攻击力",
            "生命值": "生命值",
            "防禦力": "防御力", "防薬力": "防御力",
            "暴擊率": "暴击率", "暴撃率": "暴击率",
            "暴擊傷害": "暴击伤害", "暴撃傷害": "暴击伤害",
            "共鳴效率": "共鸣效率",
            "屬性傷害加成": "属性伤害加成",
            "普攻傷害加成": "普攻伤害加成",
            "重擊傷害加成": "重击伤害加成",
            "共鳴技能傷害加成": "共鸣技能伤害加成",
            "共鳴解放傷害加成": "共鸣解放伤害加成",
            "治療效果加成": "治疗效果加成",
        }

        # 词条别名映射
        self.term_aliases = {
            "攻击力": "攻击", "攻击力%": "攻击%",
            "生命值": "生命", "生命值%": "生命%",
            "防御力": "防御", "防御力%": "防御%",
            "暴击率": "暴击", "暴击概率": "暴击",
            "暴伤": "暴击伤害", "暴击伤害%": "暴击伤害",
            "共鸣充能": "共鸣效率", "共鸣效率%": "共鸣效率",
            "元素伤害": "属性伤害加成", "元素伤害加成": "属性伤害加成",
            # 属性伤害加成的各种形式
            "湮灭伤害加成": "属性伤害加成", "导电伤害加成": "属性伤害加成",
            "热熔伤害加成": "属性伤害加成", "衍射伤害加成": "属性伤害加成",
            "气动伤害加成": "属性伤害加成", "冷凝伤害加成": "属性伤害加成",
            "湮灭伤害加成": "属性伤害加成", "导电伤害加成": "属性伤害加成",
            "热熔伤害加成": "属性伤害加成", "衍射伤害加成": "属性伤害加成",
            "气动伤害加成": "属性伤害加成", "冷凝伤害加成": "属性伤害加成"
        }
    
    def extract_text_from_image(self, image_bytes: bytes) -> Dict:
        """
        从图片中提取文字

        Args:
            image_bytes: 图片字节数据

        Returns:
            包含原始文本、置信度和处理时间的字典
        """
        start_time = time.time()

        try:
            logger.info("开始OCR文字识别...")

            # 将字节数据转换为PIL图像
            image = Image.open(BytesIO(image_bytes))
            logger.info(f"图片信息: 尺寸={image.size}, 格式={image.format}, 模式={image.mode}")

            # 转换为numpy数组
            img_array = np.array(image)

            # 使用PaddleOCR进行文字识别
            logger.info("调用PaddleOCR进行文字识别...")
            result = self.ocr.ocr(img_array, cls=True)

            # 提取文本和置信度
            raw_text = ""
            total_confidence = 0.0
            text_count = 0
            ocr_details = []

            if result and result[0]:
                logger.info(f"OCR识别到 {len(result[0])} 个文本区域")

                for i, line in enumerate(result[0]):
                    if len(line) >= 2:
                        text = line[1][0]  # 识别的文本
                        confidence = line[1][1]  # 置信度
                        bbox = line[0]  # 边界框坐标

                        raw_text += text + "\n"
                        total_confidence += confidence
                        text_count += 1

                        # 记录详细信息
                        ocr_details.append({
                            "index": i + 1,
                            "text": text,
                            "confidence": round(confidence, 3),
                            "bbox": bbox
                        })

                        logger.info(f"文本{i+1}: '{text}' (置信度: {confidence:.3f})")
            else:
                logger.warning("OCR未识别到任何文本")

            # 计算平均置信度
            avg_confidence = total_confidence / text_count if text_count > 0 else 0.0

            processing_time = time.time() - start_time

            logger.info(f"OCR识别完成: 平均置信度={avg_confidence:.3f}, 处理时间={processing_time:.3f}s")

            return {
                "raw_text": raw_text.strip(),
                "confidence": round(avg_confidence, 3),
                "processing_time": round(processing_time, 3),
                "ocr_details": ocr_details,
                "text_count": text_count
            }

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"OCR识别失败: {str(e)}")
            return {
                "raw_text": "",
                "confidence": 0.0,
                "processing_time": round(processing_time, 3),
                "error": str(e)
            }
    
    def parse_terms_from_text(self, text: str) -> List[Dict]:
        """
        从OCR文本中解析词条和数值
        使用模糊匹配

        Args:
            text: OCR识别的原始文本

        Returns:
            解析出的词条列表，每个词条包含名称、原始文本和数值
        """
        logger.info("开始解析词条...")
        logger.info(f"原始文本:\n{text}")

        terms = []
        lines = text.split('\n')
        logger.info(f"分割为 {len(lines)} 行文本")

        # 预处理：将连续的行合并处理，因为OCR可能将词条名和数值分开识别
        processed_lines = self._preprocess_lines(lines)
        logger.info(f"预处理后得到 {len(processed_lines)} 个处理单元")

        for i, line_info in enumerate(processed_lines):
            line = line_info['text'].strip()
            if not line:
                continue

            logger.info(f"处理第{i+1}个单元: '{line}'")

            # 尝试解析词条
            parsed_term = self._parse_line_enhanced(line)
            if parsed_term:
                terms.append(parsed_term)
                logger.info(f"✅ 成功解析: {parsed_term['term_name']} = {parsed_term['extracted_value']}")
            else:
                logger.info(f"❌ 无法解析: '{line}'")

        # 智能推断不完整的词条（在所有完整词条解析完成后）
        inferred_terms = self._infer_incomplete_terms(processed_lines, terms)
        terms.extend(inferred_terms)

        logger.info(f"词条解析完成，共解析出 {len(terms)} 个词条")
        return terms
    
    def _preprocess_lines(self, lines: List[str]) -> List[Dict]:
        """
        预处理OCR行，合并可能分离的词条名和数值

        Args:
            lines: OCR识别的文本行列表

        Returns:
            处理后的行信息列表
        """
        logger.info("开始预处理文本行...")
        processed = []
        i = 0

        while i < len(lines):
            current_line = lines[i].strip()
            if not current_line:
                i += 1
                continue

            # 检查当前行是否只是词条名（下一行可能是数值）
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()

                # 如果当前行是词条名，下一行是数值，则合并
                if (self._is_term_name(current_line) and
                    self._is_numeric_value(next_line)):
                    combined_text = f"{current_line} {next_line}"
                    processed.append({
                        'text': combined_text,
                        'original_lines': [current_line, next_line]
                    })
                    logger.info(f"合并行: '{current_line}' + '{next_line}' → '{combined_text}'")
                    i += 2  # 跳过下一行
                    continue

            # 否则单独处理当前行
            processed.append({
                'text': current_line,
                'original_lines': [current_line]
            })
            logger.info(f"单独处理: '{current_line}'")
            i += 1

        logger.info(f"预处理完成，{len(lines)} 行 → {len(processed)} 个处理单元")
        return processed

    def _is_term_name(self, text: str) -> bool:
        """判断文本是否可能是词条名"""
        # 常见词条名模式
        term_patterns = [
            r'攻击$', r'生命$', r'防御$', r'暴击$', r'暴击率$', r'暴击伤害$',
            r'元素精通$', r'充能效率$', r'治疗加成$', r'治疗效果加成$',
            r'普攻伤害加成$', r'重击伤害加成$',
            r'共鸣技能伤害加成$', r'共鸣解放伤害加成$', r'共鸣效率$',
            r'元素战技伤害加成$', r'元素爆发伤害加成$', r'属性伤害加成$',
            # 属性伤害加成的所有类型
            r'湮灭.*?伤害加成$', r'导电.*?伤害加成$', r'热熔.*?伤害加成$',
            r'衍射.*?伤害加成$', r'气动.*?伤害加成$', r'冷凝.*?伤害加成$',
            # 部分关键词匹配
            r'共鸣解放$', r'共鸣技能$',
            # 属性类型关键词
            r'湮灭$', r'导电$', r'热熔$', r'衍射$', r'气动$', r'冷凝$'
        ]

        for pattern in term_patterns:
            if re.search(pattern, text):
                return True
        return False

    def _is_numeric_value(self, text: str) -> bool:
        """判断文本是否是数值（可能带%）"""
        # 先清理常见的OCR错误
        cleaned_text = self._clean_numeric_text(text.strip())

        # 匹配数值模式：整数、小数、百分比
        numeric_patterns = [
            r'^\d+\.?\d*%?$',  # 基本数值和百分比
            r'^\d+\.?\d*\s*%$',  # 数值和百分比之间有空格
        ]

        for pattern in numeric_patterns:
            if re.match(pattern, cleaned_text):
                return True

        return False

    def _clean_numeric_text(self, text: str) -> str:
        """
        清理数值文本中的OCR错误

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        # 常见的OCR数值错误修正
        corrections = {
            ':': '.',  # 冒号误识别为小数点
            '：': '.',  # 中文冒号误识别为小数点
            '。': '.',  # 中文句号误识别为小数点
            'o': '0',  # 小写o误识别为0
            'O': '0',  # 大写O误识别为0
            'l': '1',  # 小写l误识别为1
            'I': '1',  # 大写I误识别为1
            'S': '5',  # 大写S误识别为5
            's': '5',  # 小写s误识别为5
        }

        cleaned = text
        for wrong, correct in corrections.items():
            cleaned = cleaned.replace(wrong, correct)

        # 处理末尾的符号（保留有意义的符号，移除无意义的符号）
        # 移除末尾的多余点号
        cleaned = re.sub(r'\.+$', '', cleaned)  # 移除末尾多余的点
        cleaned = re.sub(r'%\.+$', '%', cleaned)  # 移除%后面的点

        # 处理末尾的加减号（这些可能是有意义的符号，表示数值范围或趋势）
        # 但需要确保前面有数字
        if re.match(r'^\d+\.?\d*[+-]$', cleaned):
            # 如果是数字+符号的格式，移除符号但记录这是一个带符号的数值
            cleaned = re.sub(r'[+-]$', '', cleaned)
            logger.debug(f"检测到带符号数值，移除符号: '{text}' → '{cleaned}'")

        # 移除其他末尾的无意义符号
        cleaned = re.sub(r'[^\d.%]+$', '', cleaned)

        logger.debug(f"数值清理: '{text}' → '{cleaned}'")
        return cleaned

    def _convert_traditional_to_simplified(self, text: str) -> str:
        """
        将繁体字转换为简体字

        Args:
            text: 原始文本（可能包含繁体字）

        Returns:
            转换后的简体字文本
        """
        converted = text

        # 逐个替换繁体字
        for traditional, simplified in self.traditional_to_simplified.items():
            if traditional in converted:
                converted = converted.replace(traditional, simplified)
                logger.debug(f"繁体字转换: '{traditional}' → '{simplified}'")

        # 如果有转换，记录日志
        if converted != text:
            logger.info(f"繁体字转换: '{text}' → '{converted}'")

        return converted
    
    def fuzzy_match_term(self, text: str) -> Optional[str]:
        """
        模糊匹配词条名称 - 60%匹配度
        """
        text_clean = text.strip()

        logger.debug(f"模糊匹配: '{text_clean}'")

        # 0. 繁体字转换（优先级最高）
        text_simplified = self._convert_traditional_to_simplified(text_clean)
        if text_simplified != text_clean:
            text_clean = text_simplified

        # 1. 精确匹配
        if text_clean in self.standard_terms:
            logger.debug(f"✅ 精确匹配: '{text_clean}'")
            return text_clean

        # 2. 别名匹配
        if text_clean in self.term_aliases:
            matched = self.term_aliases[text_clean]
            logger.debug(f"✅ 别名匹配: '{text_clean}' → '{matched}'")
            return matched

        # 3. 模糊匹配 - 60%相似度，但要求长度合理
        if len(text_clean) < 2:  # 太短的文本不进行模糊匹配
            logger.debug(f"❌ 文本太短，跳过模糊匹配: '{text_clean}'")
            return None

        best_match = None
        best_similarity = 0.0
        similarity_details = []

        for term in self.standard_terms:
            similarity = self._calculate_similarity(text_clean, term)
            similarity_details.append((term, similarity))

            # 提高阈值到60%，并要求长度差不能太大
            length_diff = abs(len(text_clean) - len(term)) / max(len(text_clean), len(term))
            if similarity >= 0.6 and length_diff <= 0.5 and similarity > best_similarity:
                best_similarity = similarity
                best_match = term

        # 记录相似度详情
        similarity_details.sort(key=lambda x: x[1], reverse=True)
        top_3 = similarity_details[:3]
        logger.debug(f"相似度排名: {[(term, f'{sim:.3f}') for term, sim in top_3]}")

        if best_match:
            logger.debug(f"✅ 模糊匹配成功: '{text_clean}' → '{best_match}' (相似度: {best_similarity:.3f})")
        else:
            logger.debug(f"❌ 模糊匹配失败: '{text_clean}' (最高相似度: {top_3[0][1]:.3f})")

        return best_match
    
    def _calculate_similarity(self, s1: str, s2: str) -> float:
        """计算字符串相似度"""
        if not s1 or not s2:
            return 0.0
        
        # 使用简单的字符匹配相似度
        s1_chars = set(s1)
        s2_chars = set(s2)
        
        intersection = len(s1_chars.intersection(s2_chars))
        union = len(s1_chars.union(s2_chars))
        
        if union == 0:
            return 0.0
        
        return intersection / union

    def _parse_line_enhanced(self, line: str) -> Optional[Dict]:
        """
        增强的行解析，使用模糊匹配

        Args:
            line: 要解析的文本行

        Returns:
            解析结果字典或None
        """
        logger.debug(f"解析行: '{line}'")
        # 定义解析模式，按优先级排序
        # 支持OCR错误的数值模式：包含冒号、中文符号等，以及末尾的符号
        ocr_number_pattern = r'[0-9:：。.oOlISs]+\.?[0-9:：。.oOlISs]*[+-]?'

        patterns = [
            # 模式1: 完整词条名匹配 - 百分比词条（支持OCR错误）
            (rf'(攻击|生命|防御|暴击|暴击率|暴击伤害|元素精通|充能效率|治疗加成|普攻伤害加成|重击伤害加成|共鸣技能伤害加成|共鸣解放伤害加成|共鸣效率|属性伤害加成|治疗效果加成)\s*({ocr_number_pattern})\s*%', 'percentage'),

            # 模式2: 固定数值词条（支持OCR错误）
            (rf'(攻击|生命|防御)\s*({ocr_number_pattern})\s*$', 'fixed'),

            # 模式3: 暴击特殊处理（支持OCR错误）
            (rf'暴击\s*({ocr_number_pattern})\s*%', 'percentage_crit'),

            # 模式4: 属性伤害加成特殊匹配（支持OCR错误）
            (rf'(湮灭|导电|热熔|衍射|气动|冷凝|属性).*?伤害加成\s*({ocr_number_pattern})\s*%', 'percentage_attribute_damage'),

            # 模式5: 部分关键词匹配 - 共鸣类词条（支持OCR错误）
            (rf'(共鸣解放|共鸣技能|共鸣效率)\s*[^0-9]*\s*({ocr_number_pattern})\s*%', 'percentage_resonance'),

            # 模式6: 伤害加成通用匹配（支持OCR错误）
            (rf'([^0-9]*?)伤害加成\s*({ocr_number_pattern})\s*%', 'percentage_damage_bonus'),

            # 模式7: 通用百分比模式（支持OCR错误）
            (rf'([^0-9]+?)\s*({ocr_number_pattern})\s*%', 'percentage_generic'),

            # 模式8: 通用固定数值模式（支持OCR错误）
            (rf'([^0-9]+?)\s*({ocr_number_pattern})\s*$', 'fixed_generic')
        ]

        for i, (pattern, pattern_type) in enumerate(patterns):
            match = re.search(pattern, line)
            if match:
                groups = match.groups()
                logger.debug(f"匹配模式{i+1} ({pattern_type}): {groups}")

                if pattern_type == 'percentage_crit':
                    # 暴击特殊处理
                    term_name = "暴击"
                    value_str = groups[0].strip()
                    is_percentage = True
                    logger.debug(f"暴击特殊处理: {term_name} = {value_str}%")
                elif pattern_type == 'percentage_attribute_damage':
                    # 属性伤害加成统一处理
                    term_name = "属性伤害加成"
                    value_str = groups[1].strip()
                    is_percentage = True
                    logger.debug(f"属性伤害加成处理: {groups[0]} → {term_name} = {value_str}%")
                elif pattern_type == 'percentage_resonance':
                    # 共鸣类词条关键词映射
                    keyword = groups[0].strip()
                    value_str = groups[1].strip()
                    is_percentage = True

                    # 关键词映射到完整词条名
                    resonance_mapping = {
                        "共鸣解放": "共鸣解放伤害加成",
                        "共鸣技能": "共鸣技能伤害加成",
                        "共鸣效率": "共鸣效率"
                    }
                    term_name = resonance_mapping.get(keyword, keyword)
                    logger.debug(f"共鸣词条映射: {keyword} → {term_name} = {value_str}%")
                elif pattern_type == 'percentage_damage_bonus':
                    # 伤害加成通用处理
                    raw_term = groups[0].strip()
                    value_str = groups[1].strip()
                    is_percentage = True

                    # 使用模糊匹配
                    matched_term = self.fuzzy_match_term(raw_term + "伤害加成")
                    if matched_term:
                        term_name = matched_term
                        logger.debug(f"伤害加成模糊匹配: {raw_term} → {term_name} = {value_str}%")
                    else:
                        logger.debug(f"伤害加成模糊匹配失败: {raw_term}")
                        continue
                else:
                    # 其他模式的标准处理
                    term_name = groups[0].strip()
                    value_str = groups[1].strip()
                    is_percentage = 'percentage' in pattern_type
                    logger.debug(f"标准处理: {term_name} = {value_str}{'%' if is_percentage else ''}")

                # 使用模糊匹配清理词条名称
                if pattern_type not in ['percentage_attribute_damage', 'percentage_resonance', 'percentage_damage_bonus']:
                    matched_term = self.fuzzy_match_term(term_name)
                    if matched_term:
                        if matched_term != term_name:
                            logger.debug(f"词条名模糊匹配: '{term_name}' → '{matched_term}'")
                        term_name = matched_term
                    else:
                        # 如果模糊匹配失败，跳过这个词条
                        logger.debug(f"词条名模糊匹配失败，跳过: '{term_name}'")
                        continue

                # 根据是否为百分比确定最终词条名
                final_term_name = self._determine_final_term_name(term_name, is_percentage)
                if final_term_name != term_name:
                    logger.debug(f"最终词条名确定: '{term_name}' → '{final_term_name}'")

                # 转换数值（先清理OCR错误）
                try:
                    cleaned_value_str = self._clean_numeric_text(value_str)
                    if is_percentage:
                        # 移除%符号后转换
                        numeric_part = cleaned_value_str.replace('%', '').strip()
                        value = float(numeric_part)
                    else:
                        value = float(cleaned_value_str)

                    result = {
                        "term_name": final_term_name,
                        "source_text": line,
                        "extracted_value": value,
                        "is_percentage": is_percentage
                    }

                    logger.debug(f"✅ 解析成功: {final_term_name} = {value}{'%' if is_percentage else ''}")
                    return result

                except ValueError as e:
                    logger.debug(f"数值转换失败: '{value_str}' - {str(e)}")
                    continue

        logger.debug(f"❌ 所有模式都无法匹配: '{line}'")
        return None

    def _determine_final_term_name(self, term_name: str, is_percentage: bool) -> str:
        """
        根据是否为百分比确定最终词条名
        """
        # 需要区分百分比和固定值的词条
        percentage_terms = {
            "攻击": "攻击%" if is_percentage else "攻击",
            "生命": "生命%" if is_percentage else "生命",
            "防御": "防御%" if is_percentage else "防御",
        }

        return percentage_terms.get(term_name, term_name)

    def validate_image(self, image_bytes: bytes) -> Tuple[bool, str]:
        """
        验证图片是否有效

        Args:
            image_bytes: 图片字节数据

        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 检查文件大小
            if len(image_bytes) > settings.MAX_FILE_SIZE:
                return False, f"文件大小超过限制 ({settings.MAX_FILE_SIZE} bytes)"

            # 尝试打开图片
            image = Image.open(BytesIO(image_bytes))

            # 检查图片格式
            allowed_formats = ['jpeg', 'jpg', 'png', 'mpo']  # 基本支持的格式，添加MPO支持
            if image.format and image.format.lower() not in allowed_formats:
                return False, f"不支持的图片格式: {image.format}"

            # 对于无法识别格式的图片，如果能正常打开就允许（可能是MPO等特殊格式）
            if not image.format:
                logger.info("图片格式未识别，但PIL能正常打开，允许处理")

            # 检查图片尺寸
            width, height = image.size
            if width < 50 or height < 50:
                return False, "图片尺寸太小"

            if width > 4096 or height > 4096:
                return False, "图片尺寸太大"

            return True, ""

        except Exception as e:
            return False, f"图片验证失败: {str(e)}"

    def _infer_incomplete_terms(self, processed_lines: List[Dict], existing_terms: List[Dict]) -> List[Dict]:
        """
        智能推断不完整的词条

        Args:
            processed_lines: 预处理后的行信息
            existing_terms: 已经解析成功的词条

        Returns:
            推断出的词条列表
        """
        logger.info("开始智能推断不完整的词条...")

        inferred_terms = []

        # 获取已解析的词条名列表
        existing_term_names = [term['term_name'] for term in existing_terms]
        logger.debug(f"已解析的词条: {existing_term_names}")

        # 查找未解析的行
        unresolved_lines = []
        for line_info in processed_lines:
            text = line_info['text']
            # 检查是否是单独的词条片段或数值
            if self._is_incomplete_term_fragment(text) or self._is_orphaned_value(text):
                unresolved_lines.append(line_info)

        logger.info(f"发现 {len(unresolved_lines)} 个未解析的行: {[line['text'] for line in unresolved_lines]}")

        # 尝试配对词条片段和数值
        for i, line_info in enumerate(unresolved_lines):
            text = line_info['text']

            if self._is_incomplete_term_fragment(text):
                # 寻找附近的数值
                nearby_value = self._find_nearby_value(unresolved_lines, i)
                if nearby_value:
                    inferred_term = self._infer_term_from_fragment_and_value(
                        text, nearby_value, existing_term_names
                    )
                    if inferred_term:
                        inferred_terms.append(inferred_term)
                        logger.info(f"✅ 智能推断成功: {inferred_term['term_name']} = {inferred_term['extracted_value']} (从 '{text}' + '{nearby_value}')")

        logger.info(f"智能推断完成，推断出 {len(inferred_terms)} 个词条")
        return inferred_terms

    def _is_incomplete_term_fragment(self, text: str) -> bool:
        """判断是否是不完整的词条片段"""
        text = text.strip()

        # 完整的词条名（单独出现时也应该被识别为片段，等待数值配对）
        complete_terms = [
            '攻击', '生命', '防御', '暴击', '暴击伤害', '共鸣效率',
            '属性伤害加成', '普攻伤害加成', '重击伤害加成',
            '共鸣技能伤害加成', '共鸣解放伤害加成', '治疗效果加成'
        ]

        # 检查是否是完整的词条名（单独出现，没有数值）
        if text in complete_terms:
            logger.debug(f"识别到完整词条名片段: '{text}'")
            return True

        # 常见的词条片段
        fragments = [
            '击',      # 暴击、攻击、重击
            '玫击',    # 暴击的OCR错误
            '攻',      # 攻击
            '防',      # 防御
            '生命',    # 生命
            '暴',      # 暴击
            '伤害',    # 各种伤害加成
            '效率',    # 共鸣效率
            '加成',    # 各种加成
            '解放',    # 共鸣解放
            '技能',    # 共鸣技能
            '普攻',    # 普攻伤害加成
            '重',      # 重击
        ]

        # 检查是否匹配片段模式
        for fragment in fragments:
            if fragment in text and len(text) <= 4:  # 短文本且包含关键片段
                logger.debug(f"识别到词条片段: '{text}' (包含'{fragment}')")
                return True

        # 检查是否是单个字符的关键词
        if len(text) == 1 and text in ['击', '攻', '防', '暴', '生', '命']:
            logger.debug(f"识别到单字符词条片段: '{text}'")
            return True

        return False

    def _is_orphaned_value(self, text: str) -> bool:
        """判断是否是孤立的数值"""
        text = text.strip()

        # 先检查原始文本是否包含带符号的数值模式
        if re.match(r'^\d+\.?\d*[+-]$', text):
            logger.debug(f"识别到带符号数值: '{text}'")
            return True

        # 检查是否是纯数值（包括百分比）
        cleaned_text = self._clean_numeric_text(text)
        is_valid = bool(re.match(r'^\d+\.?\d*%?$', cleaned_text))

        if is_valid:
            logger.debug(f"识别到孤立数值: '{text}' → '{cleaned_text}'")

        return is_valid

    def _find_nearby_value(self, unresolved_lines: List[Dict], current_index: int) -> Optional[str]:
        """在附近的行中寻找数值"""
        # 优先检查后面的行（词条片段通常在数值前面）
        for offset in [1, 2, -1, -2]:
            target_index = current_index + offset
            if 0 <= target_index < len(unresolved_lines):
                target_text = unresolved_lines[target_index]['text']
                if self._is_orphaned_value(target_text):
                    # 优先选择百分比数值（更可能是词条数值）
                    if '%' in target_text:
                        return target_text

        # 如果没有找到百分比数值，再找固定数值
        for offset in [1, 2, -1, -2]:
            target_index = current_index + offset
            if 0 <= target_index < len(unresolved_lines):
                target_text = unresolved_lines[target_index]['text']
                if self._is_orphaned_value(target_text) and '%' not in target_text:
                    return target_text

        return None

    def _infer_term_from_fragment_and_value(self, fragment: str, value_text: str, existing_terms: List[str]) -> Optional[Dict]:
        """
        根据词条片段和数值推断完整词条

        Args:
            fragment: 词条片段（如"击"、"玫击"）
            value_text: 数值文本（如"10.5%"）
            existing_terms: 已存在的词条名列表

        Returns:
            推断出的词条信息，如果无法推断则返回None
        """
        logger.debug(f"推断词条: 片段='{fragment}', 数值='{value_text}', 已有词条={existing_terms}")

        # 清理数值
        cleaned_value = self._clean_numeric_text(value_text)
        is_percentage = '%' in cleaned_value

        try:
            numeric_value = float(cleaned_value.replace('%', '').strip())
        except ValueError:
            logger.debug(f"无法转换数值: {cleaned_value}")
            return None

        # 根据片段类型进行推断
        candidates = self._get_term_candidates_by_fragment(fragment)
        logger.debug(f"候选词条: {candidates}")

        if not candidates:
            return None

        # 根据数值范围和已有词条进行筛选
        best_match = self._select_best_term_match(
            candidates, numeric_value, is_percentage, existing_terms
        )

        if best_match:
            logger.debug(f"最佳匹配: {best_match}")
            return {
                'term_name': best_match,
                'extracted_value': numeric_value,
                'original_text': f"{fragment} {value_text}",
                'source_text': f"{fragment} {value_text}",  # 添加source_text字段
                'inferred': True  # 标记为推断得出
            }

        return None

    def _get_term_candidates_by_fragment(self, fragment: str) -> List[str]:
        """根据片段获取候选词条（按优先级排序）"""
        fragment = fragment.strip()

        # 先转换繁体字
        fragment = self._convert_traditional_to_simplified(fragment)

        # 如果是完整的词条名，直接返回该词条的两种形式（固定值和百分比）
        complete_term_mapping = {
            '攻击': ['攻击', '攻击%'],
            '生命': ['生命', '生命%'],
            '防御': ['防御', '防御%'],
            '暴击': ['暴击'],  # 暴击只有百分比形式
            '暴击伤害': ['暴击伤害'],  # 暴击伤害只有百分比形式
            '共鸣效率': ['共鸣效率'],  # 共鸣效率只有百分比形式
            '属性伤害加成': ['属性伤害加成'],  # 属性伤害加成只有百分比形式
            '普攻伤害加成': ['普攻伤害加成'],
            '重击伤害加成': ['重击伤害加成'],
            '共鸣技能伤害加成': ['共鸣技能伤害加成'],
            '共鸣解放伤害加成': ['共鸣解放伤害加成'],
            '治疗效果加成': ['治疗效果加成']
        }

        if fragment in complete_term_mapping:
            candidates = complete_term_mapping[fragment]
            logger.debug(f"完整词条名 '{fragment}' 的候选词条: {candidates}")
            return candidates

        # 词条片段映射（按优先级排序）
        fragment_mapping = {
            # 击：优先暴击和攻击，重击伤害加成字符太多不太可能只识别一个字
            '击': ['暴击', '攻击', '攻击%'],

            # 玫击：字形更接近攻击，优先判断为攻击
            '玫击': ['攻击', '攻击%', '暴击'],

            # 繁体字相关的片段
            '撃': ['暴击', '攻击', '攻击%'],  # 繁体的击
            '擊': ['暴击', '攻击', '攻击%'],  # 另一种繁体的击
            '擎': ['攻击', '攻击%'],  # 攻擎的擎
            '薬': ['防御', '防御%'],  # 防薬的薬

            # 其他片段保持原有逻辑
            '攻': ['攻击', '攻击%'],
            '防': ['防御', '防御%'],
            '生命': ['生命', '生命%'],
            '暴': ['暴击', '暴击伤害'],
            '伤害': ['暴击伤害', '属性伤害加成', '普攻伤害加成', '重击伤害加成', '共鸣技能伤害加成', '共鸣解放伤害加成'],
            '效率': ['共鸣效率'],
            '加成': ['属性伤害加成', '普攻伤害加成', '重击伤害加成', '共鸣技能伤害加成', '共鸣解放伤害加成', '治疗效果加成'],
            '解放': ['共鸣解放伤害加成'],
            '技能': ['共鸣技能伤害加成'],
            '普攻': ['普攻伤害加成'],
            '重': ['重击伤害加成'],
        }

        candidates = fragment_mapping.get(fragment, [])
        logger.debug(f"片段 '{fragment}' 的候选词条（按优先级）: {candidates}")
        return candidates

    def _select_best_term_match(self, candidates: List[str], value: float, is_percentage: bool, existing_terms: List[str]) -> Optional[str]:
        """
        根据数值范围和已有词条选择最佳匹配

        Args:
            candidates: 候选词条列表
            value: 数值
            is_percentage: 是否为百分比
            existing_terms: 已存在的词条名列表

        Returns:
            最佳匹配的词条名
        """
        logger.debug(f"选择最佳匹配: 候选={candidates}, 数值={value}, 百分比={is_percentage}")

        # 定义主词条数值范围（基于词条数值表）
        main_term_ranges = {
            # COST4主词条
            '暴击': (22.0, 22.0) if is_percentage else None,
            '暴击伤害': (44.0, 44.0) if is_percentage else None,
            '治疗效果加成': (26.4, 26.4) if is_percentage else None,
            '生命%': (33.0, 33.0) if is_percentage else None,
            '防御%': (41.8, 41.8) if is_percentage else None,
            '攻击%': (33.0, 33.0) if is_percentage else None,
            '攻击': (150, 150) if not is_percentage else None,

            # COST3主词条
            '属性伤害加成': (30.0, 30.0) if is_percentage else None,
            '共鸣效率': (32.0, 32.0) if is_percentage else None,
            '生命': (30.0, 30.0) if is_percentage else None,
            '防御': (38.0, 38.0) if is_percentage else None,

            # COST1主词条
            '生命': (2280, 2280) if not is_percentage else (22.8, 22.8) if is_percentage else None,
            '防御': (18.0, 18.0) if is_percentage else None,
            '攻击': (18.0, 18.0) if is_percentage else None,
        }

        # 定义副词条固定数值档位（严格基于词条数值表）
        sub_term_values = {
            # 攻击固定值档位
            '攻击': [30, 40, 50] if not is_percentage else None,
            # 防御固定值档位
            '防御': [40, 50, 60] if not is_percentage else None,
            # 生命固定值档位
            '生命': [320, 360, 390, 430, 470, 510, 540, 580] if not is_percentage else None,
            # 攻击百分比档位
            '攻击%': [6.46, 7.10, 7.90, 8.60, 9.40, 10.10, 10.90, 11.60] if is_percentage else None,
            # 防御百分比档位
            '防御%': [8.10, 9.0, 10.0, 10.90, 11.80, 12.80, 13.80, 15.0] if is_percentage else None,
            # 生命百分比档位
            '生命%': [6.40, 7.10, 7.90, 8.60, 9.40, 10.10, 10.90, 11.60] if is_percentage else None,
            # 暴击率档位
            '暴击': [6.30, 6.90, 7.50, 8.10, 8.70, 9.30, 9.90, 10.50] if is_percentage else None,
            # 暴击伤害档位
            '暴击伤害': [12.6, 13.8, 15.0, 16.2, 17.4, 18.6, 19.8, 21.0] if is_percentage else None,
            # 共鸣效率档位
            '共鸣效率': [6.80, 7.60, 8.40, 9.20, 10.00, 10.80, 11.60, 12.40] if is_percentage else None,
            # 各种伤害加成档位（都是相同的）
            '共鸣技能伤害加成': [6.40, 7.10, 7.90, 8.60, 9.40, 10.10, 10.90, 11.60] if is_percentage else None,
            '共鸣解放伤害加成': [6.40, 7.10, 7.90, 8.60, 9.40, 10.10, 10.90, 11.60] if is_percentage else None,
            '普攻伤害加成': [6.40, 7.10, 7.90, 8.60, 9.40, 10.10, 10.90, 11.60] if is_percentage else None,
            '重击伤害加成': [6.40, 7.10, 7.90, 8.60, 9.40, 10.10, 10.90, 11.60] if is_percentage else None,
        }

        # 首先检查是否为主词条
        for candidate in candidates:
            main_range = main_term_ranges.get(candidate)
            if main_range and abs(value - main_range[0]) <= 0.05:  # 允许0.05的误差，考虑OCR识别误差
                logger.debug(f"匹配主词条: {candidate} (数值={value})")
                return candidate

        # 然后检查副词条，按候选词条的优先级顺序检查
        for candidate in candidates:  # candidates已经按优先级排序
            # 检查是否已存在相同类型的词条
            if not self._has_conflicting_term(candidate, existing_terms):
                valid_values = sub_term_values.get(candidate)
                if valid_values:
                    # 检查数值是否匹配任何一个固定档位（允许0.05的误差，考虑OCR识别误差）
                    for valid_value in valid_values:
                        if abs(value - valid_value) <= 0.05:
                            logger.debug(f"匹配副词条: {candidate} (数值={value}, 档位={valid_value})")
                            return candidate

        logger.debug(f"无法匹配任何词条")
        return None

    def _has_conflicting_term(self, candidate: str, existing_terms: List[str]) -> bool:
        """检查是否与已有词条冲突"""

        # 游戏机制：主词条和副词条可以共存！
        # 例如：暴击主词条(22.0%) + 暴击副词条(10.5%) 是完全合法的
        #
        # 只有在以下情况才算冲突：
        # 1. 完全相同的词条名且数值范围相同（即两个副词条重复）
        # 2. 但主词条和副词条不冲突，因为它们数值范围不同

        # 简化逻辑：由于我们的推断是基于数值档位的，
        # 如果数值能匹配档位，说明这是一个合法的词条组合
        # 不需要复杂的冲突检查

        return False


# 创建全局OCR服务实例
ocr_service = OCRService()
