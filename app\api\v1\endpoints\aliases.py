"""
别名管理API端点
处理角色别名的增删改查功能
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.alias_service import alias_service
from app.schemas.score import (
    AliasListResponse, 
    AliasUpdateRequest, 
    AliasUpdateResponse,
    ErrorResponse
)

router = APIRouter()


@router.get("/", response_model=AliasListResponse)
def get_all_aliases(
    *,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取所有角色别名配置
    
    Returns:
        所有角色的别名映射
    """
    try:
        aliases = alias_service.get_all_aliases()
        
        return AliasListResponse(
            aliases=aliases,
            total_characters=len(aliases)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取别名配置失败: {str(e)}"
        )


@router.get("/{character_name}")
def get_character_aliases(
    *,
    character_name: str,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取指定角色的别名列表
    
    Args:
        character_name: 角色名称
        
    Returns:
        角色的别名列表
    """
    try:
        aliases = alias_service.get_character_aliases(character_name)
        
        if aliases is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色 '{character_name}' 不存在"
            )
        
        return {
            "character_name": character_name,
            "aliases": aliases,
            "total_aliases": len(aliases)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色别名失败: {str(e)}"
        )


@router.put("/{character_name}", response_model=AliasUpdateResponse)
def update_character_aliases(
    *,
    character_name: str,
    request: AliasUpdateRequest,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    更新指定角色的别名列表
    
    Args:
        character_name: 角色名称
        request: 别名更新请求
        
    Returns:
        更新结果
    """
    try:
        # 验证角色名称一致性
        if character_name != request.character_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="URL中的角色名称与请求体中的角色名称不一致"
            )
        
        # 验证别名唯一性
        for alias in request.aliases:
            if alias != character_name:  # 角色本名可以重复
                is_unique, conflict_msg = alias_service.validate_alias_uniqueness(character_name, alias)
                if not is_unique:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=conflict_msg
                    )
        
        # 更新别名
        success, message = alias_service.update_character_aliases(character_name, request.aliases)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        # 获取更新后的别名列表
        updated_aliases = alias_service.get_character_aliases(character_name)
        
        return AliasUpdateResponse(
            success=True,
            message=message,
            character_name=character_name,
            aliases=updated_aliases or []
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新别名失败: {str(e)}"
        )


@router.post("/{character_name}/add-alias")
def add_character_alias(
    *,
    character_name: str,
    alias: str,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    为指定角色添加新别名
    
    Args:
        character_name: 角色名称
        alias: 新别名
        
    Returns:
        添加结果
    """
    try:
        success, message = alias_service.add_character_alias(character_name, alias)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        # 获取更新后的别名列表
        updated_aliases = alias_service.get_character_aliases(character_name)
        
        return {
            "success": True,
            "message": message,
            "character_name": character_name,
            "new_alias": alias,
            "aliases": updated_aliases or []
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加别名失败: {str(e)}"
        )


@router.delete("/{character_name}/remove-alias")
def remove_character_alias(
    *,
    character_name: str,
    alias: str,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    移除指定角色的别名
    
    Args:
        character_name: 角色名称
        alias: 要移除的别名
        
    Returns:
        移除结果
    """
    try:
        success, message = alias_service.remove_character_alias(character_name, alias)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        # 获取更新后的别名列表
        updated_aliases = alias_service.get_character_aliases(character_name)
        
        return {
            "success": True,
            "message": message,
            "character_name": character_name,
            "removed_alias": alias,
            "aliases": updated_aliases or []
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"移除别名失败: {str(e)}"
        )


@router.delete("/{character_name}")
def delete_character(
    *,
    character_name: str,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    删除指定角色及其所有别名
    
    Args:
        character_name: 角色名称
        
    Returns:
        删除结果
    """
    try:
        success, message = alias_service.delete_character(character_name)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        return {
            "success": True,
            "message": message,
            "deleted_character": character_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除角色失败: {str(e)}"
        )


@router.get("/search/{alias}")
def find_character_by_alias(
    *,
    alias: str,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    根据别名查找角色
    
    Args:
        alias: 别名
        
    Returns:
        匹配的角色信息
    """
    try:
        character_name = alias_service.find_character_by_alias(alias)
        
        if character_name is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到别名 '{alias}' 对应的角色"
            )
        
        # 获取角色的所有别名
        all_aliases = alias_service.get_character_aliases(character_name)
        
        return {
            "search_alias": alias,
            "character_name": character_name,
            "all_aliases": all_aliases or [],
            "is_exact_match": alias.lower() == character_name.lower()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查找角色失败: {str(e)}"
        )


@router.get("/statistics/summary")
def get_alias_statistics(
    *,
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    获取别名统计信息
    
    Returns:
        别名统计数据
    """
    try:
        stats = alias_service.get_statistics()
        
        return {
            "statistics": stats,
            "timestamp": alias_service._get_current_date()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )
